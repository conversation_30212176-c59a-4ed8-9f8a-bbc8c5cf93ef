<%@ page import  = 'javax.servlet.http.Cookie' %>
<g:render template="/evidya/navheader_new"/>
<style>
.modal.fade .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(.5);transform: translate(0,0)scale(.5);}
.modal.show .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(1);transform: translate(0,0)scale(1);}
.signupDummy,#agree{
    width: 100%;
    background: #233982;
    color: #ffffff;
}

/* Store page specific styles */
body {
    background: #ffffff !important;
}

.evidyaStore {
    background: #ffffff !important;
}

/* Modern grid layout for books */
.book-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.book-card {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
}

.book-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.book-cover-container {
    position: relative;
    width: 100%;
    padding-top: 140%; /* 3:4 aspect ratio for portrait */
    overflow: hidden;
}

.book-cover-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.book-info {
    padding: 15px;
}

.book-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.book-author {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.book-language {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
    color: white;
    background: #233982;
}

/* Filter improvements */
.Sidewrapper {
    background: #ffffff !important;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.Sidewrapper h4 {
    color: #333 !important;
    font-size: 16px !important;
    margin-bottom: 15px !important;
    padding-bottom: 8px;
    border-bottom: 2px solid #3499FF;
}

.Sidewrapper .card {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

.Sidewrapper ul li {
    padding: 8px 0 !important;
    border-bottom: 1px solid #f0f0f0;
}

.Sidewrapper ul li:last-child {
    border-bottom: none;
}

.Sidewrapper ul li a {
    color: #555 !important;
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: block;
}

.Sidewrapper ul li a:hover {
    background: #f8f9fa;
    color: #3499FF !important;
    text-decoration: none;
}

.categories {
    background: #f8f9fa !important;
    border-radius: 6px !important;
    margin-bottom: 8px !important;
}

.categories:hover {
    background: #e9ecef !important;
}

/* Search improvements */
.search {
    border: 2px solid #e0e0e0 !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.search:focus {
    border-color: #3499FF !important;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 153, 255, 0.1);
}

/* Mobile responsive */
@media (max-width: 768px) {
    .book-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
        padding: 15px 0;
    }

    .book-cover-container {
        padding-top: 130%;
    }

    .book-info {
        padding: 12px;
    }

    .book-title {
        font-size: 13px;
    }
}
</style>
<section class="evidyaStore mt-5">
    <div class="loading-icon">
        <div class="loader-wrapper">
            <div class="loader">Loading</div>
        </div>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-4 col-lg-4 d-none d-lg-block">
               <div class="Sidewrapper">
                   <input type="text" class="search" placeholder="Search(title,subject,author)" id="search-book" autocomplete="off" value="${params.searchString}">
                   <h4 class="mt-4 ml-1">- Filter by Language</h4>
                   <div class="card">
                       <ul class="mt-2" id="langaugeList">
                           <li><a href="javascript:displayBooksDisp('language','English')">English</a></li>
                           <li><a href="javascript:displayBooksDisp('language','Hindi')">Hindi</a></li>
                           <li><a href="javascript:displayBooksDisp('language','Marathi')">Marathi</a></li>
                           <li><a href="javascript:displayBooksDisp('language','Bengali')">Bengali</a></li>
                       </ul>
                   </div>
                   <h4 class="mt-4 ml-1">- Filter by Discipline</h4>
                   <ul class="card subCategory">
                       <li class="main-list mt-2">
                           <a class="categories" id="management"><span>Business & Management</span> <i class="material-icons add">add</i><i class="material-icons minimize">remove</i> </a>
                       <ul class="sub-menu" id="Business">
                       </ul>
                       </li>
                       <li class="main-list">
                           <a class="categories" id="social"><span>Social Science</span> <i class="material-icons add">add</i><i class="material-icons minimize">remove</i></a>
                           <ul class="sub-menu" id="SocialScience">
                           </ul>
                       </li>
                       <li class="main-list">
                           <a class="categories" id="fiction"><span>Non-Fiction</span> <i class="material-icons add">add</i><i class="material-icons minimize">remove</i></a>
                           <ul class="sub-menu" id="NonFiction">
                           </ul>
                       </li>
                       <li class="main-list">
                           <a class="categories" id="upsctoggle"><span>UP<small style="font-size: 100%;font-weight: bold;position: relative;left: -0.75px;">SC</small> Resources</span> <i class="material-icons add">add</i><i class="material-icons minimize">remove</i></a>
                           <ul class="sub-menu" id="UPSC">
                           </ul>
                       </li>
                       <li class="main-list">
                           <a class="categories" id="spectrumToggle"><span>SAGE Spectrum</span> <i class="material-icons add">add</i><i class="material-icons minimize">remove</i></a>
                           <ul class="sub-menu" id="spectrum">
                           </ul>
                       </li>
                   </ul>

               </div>
            </div>
            <div class="col-sm-12 col-lg-8" >
               <div class="d-flex align-items-center result-wrapper" id="displayResults">
               </div>
                <div class="d-flex align-items-center justify-content-between mt-4 showResult">
                  <div>
                        <p class="itemLabel" id="topPaginationMessage"></p>
                    </div>
                    <div>

                        <select id="sortBy" name="sortBy" onchange="sortDisplay();">
                            <option selected>Sort by</option>
                            <option value="title">Title(A-Z)</option>
                            <option value="title">Title(Z-A)</option>
                            <option value="author">Author(A-Z)</option>
                            <option value="author">Author(Z-A)</option>
                        </select>
                    </div>
                    </div>
                <p id="searcherrormsg" style="display: none"></p>
                    <div class="mt-4 bg-gray" id="booksDisplayList">

                    </div>
  <div class="row ml-0 mr-0 align-items-center mt-4 bg-gray border-pagination" id="bottomPagination" style="display: none">
                    <div class="col-6">
                        <ul class="pagination" id="paginationList"></ul>
                    </div>
                </div>
                </div>
            </div>
    </div>
</section>
<section>
    <div class="modal fade" id="popupLogin">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <p>We're so glad you liked this title! <a class="cmn-login loginnow" onclick="javascript:evidyaloginOpen();">Login Now</a> to add this book to your library or create a customized wishlist.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal -->
<div class="modal fade" id="evidyaTCModal" data-keyboard="false" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="evidyaTCModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-zoom" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-center" id="evidyaTCModalLabel">Terms and conditions</h5>
            </div>
            <div class="modal-body">
                <p class="">Our T&C has been revised, please <a href="/evidya/termsCondition" style="font-size: 16px" target="_blank">click</a> to read and accept to enjoy seamless services in your account.</p>
                <div class="">
                    <div class="mt-3 d-flex align-items-center">
                        <input class="mr-2" type="checkbox" id="termsConditionCheck" name="termsConditionCheck" required>
                        <label for="termsConditionCheck" class="mb-0">I have read and agree to the  <a class="mt-2" href="/evidya/termsCondition" target="_blank">Terms of Service</a></label>
                    </div>
                    <div id="agreeButtonWrapper"></div>
                    <input type="button" id="agreeDummy" class="btn btn-lg continue mt-3 signupDummy" value="Continue" disabled>
                </div>
            </div>
        </div>
    </div>
</div>

<g:render template="/evidya/footer_new"/>
<script>
   $("#management").click(function(){
       $('#Business').toggle();
       $(this).toggleClass('minus');
   });

   $("#social").click(function(){
       $('#SocialScience').toggle();
       $(this).toggleClass('minus');
   });
   $("#fiction").click(function(){
       $('#NonFiction').toggle();
       $(this).toggleClass('minus');
   });
   $("#upsctoggle").click(function(){
       $('#UPSC').toggle();
       $(this).toggleClass('minus');
   });
   $("#spectrumToggle").click(function(){
       $('#spectrum').toggle();
       $(this).toggleClass('minus');
   });

    var searchMode=false;
    var books ;
    var booksTags;
    var indexSubject="${params.subject}";
   var homepage="${params.homepage}";
    var indexGrade="${params.grade}";
    var indexLanguage="${params.language}";
    var sortBy="title";
    var previousMode="";
    var previousModeValue="";
    var bkRetry = 1;
    var callingFirstTime=true;
   var booksserch=[];
    function getBookCategories(){
        <g:remoteFunction controller="evidya" action="getBookCategories"  onSuccess='intitializeCategories(data);'  params="'categories=level&level=College&apiMode=optimized'"/>
    }

    function sortDisplay(){
        if(document.getElementById("sortBy").selectedIndex==1) {
            books.sort(SortByTitle);
        }else if(document.getElementById("sortBy").selectedIndex==2) {
            books.sort(SortByTitle);
            books.reverse(SortByTitle);
        } else if(document.getElementById("sortBy").selectedIndex==3) {
            books.sort(SortByAuthor);
        }else if(document.getElementById("sortBy").selectedIndex==4) {
            books.sort(SortByAuthor);
            books.reverse(SortByAuthor);
        }

        displayBooks(previousMode,previousModeValue);
    }

    function intitializeCategories(data){
        if((data==null || data.bookCategories==null || data.results==null || data.length==0) && bkRetry==1) {
            bkRetry++;
            getBookCategories();
            return;
        }

        if(data.bookCategories!=null&&"true"==data.bookCategories) bookCategories=true;

        if(data.status!="Nothing present"){
            var data1 = JSON.parse(data.results);
            var formattedTopicMapIndex = formatDataIndex(JSON.parse(data.results));
            var disciplines = formattedTopicMapIndex.subjects["CollegeSAGEBusiness"];
            var disciplineStr="";

            for(j=0;j<disciplines.length;j++){
                disciplineStr+="<li>\n" +
                    "    <a href=\"javascript:displayBooksDisp('subject','"+disciplines[j]+"','Business')\">"+disciplines[j]+"</a>\n" +
                    "    </li>";
            }

            disciplines = formattedTopicMapIndex.subjects["CollegeSAGESocialScience"];
            document.getElementById("Business").innerHTML=disciplineStr;
            disciplineStr="";
            disciplineList=document.getElementById("SocialScience");

            for(j=0;j<disciplines.length;j++){
                disciplineStr+="<li>\n" +
                       "    <a href=\"javascript:displayBooksDisp('subject','"+disciplines[j]+"','Social Science')\">"+disciplines[j]+"</a>\n" +
                        "    </li>";
            }

            document.getElementById("SocialScience").innerHTML=disciplineStr;
            var disciplines = formattedTopicMapIndex.subjects["CollegeSAGENonFiction"];
            var disciplineStr="";

            for(j=0;j<disciplines.length;j++){
                disciplineStr+="<li>\n" +
                    "    <a href=\"javascript:displayBooksDisp('subject','"+disciplines[j]+"','Non-Fiction')\">"+disciplines[j]+"</a>\n" +
                    "    </li>";
            }
            document.getElementById("NonFiction").innerHTML=disciplineStr;

            //for UPSC
            var disciplines = formattedTopicMapIndex.subjects["CollegeSAGEUPSCResources"];
            var disciplineStr="";
            for(j=0;j<disciplines.length;j++){
                disciplineStr+="<li>\n" +
                    "    <a href=\"javascript:displayBooksDisp('subject','"+disciplines[j]+"','UPSC Resources')\">"+disciplines[j]+"</a>\n" +
                    "    </li>";
            }
            document.getElementById("UPSC").innerHTML=disciplineStr;

            //for SAGE Spectrum
            var disciplines = formattedTopicMapIndex.subjects["CollegeSAGESAGESpectrum"];
            var disciplineStr="";
            for(j=0;j<disciplines.length;j++){
                disciplineStr+="<li>\n" +
                    "    <a href=\"javascript:displayBooksDisp('subject','"+disciplines[j]+"','SAGE Spectrum')\">"+disciplines[j]+"</a>\n" +
                    "    </li>";
            }
            document.getElementById("spectrum").innerHTML=disciplineStr;


            myLibraryMode=false;
            instituteLibraryCalled=false;
            if(homepage!="filter") {
                storeBooksReceived(data);
            }
            getBooksList();

        }
    }

    function getBooksList() {
        $('.loading-icon').removeClass('hidden');
        myLibraryMode=false;
        instituteLibraryCalled=false;
        <g:remoteFunction controller="wonderpublish" action="getBooksListEvidya"  onSuccess='storeBooksReceived(data);'
               params="'categories=level&level=College'" />
    }

    var blRetry = 1;
    function storeBooksReceived(data){
        if((data==null || data.length==0) && blRetry==1) {
            blRetry++;
            <g:remoteFunction controller="wonderpublish" action="getBooksListEvidya"  onSuccess='storeBooksReceived(data);'
                    params="'categories=level&level=College'" />
            return;
        }
        storeBooksData = data;
        <g:remoteFunction controller="institute" action="getLibraryBookIds"  onSuccess='libraryBooksReceived(data);' />
    }

    var lbRetry = 1;
    function libraryBooksReceived(data){
        if((data==null || data.length==0) && lbRetry==1) {
            lbRetry++;
            <g:remoteFunction controller="institute" action="getLibraryBookIds"  onSuccess='libraryBooksReceived(data);' />
            return;
        }

        if(data.status=="OK") {
            libraryBooksData = data;
            libraryBooksList = data.libraryBookIds;
        }
      if(searchMode) submitSearch();
        else booksListReceived1(storeBooksData);
    }

    function SortByTitle(x,y) {
        return ((x.title == y.title) ? 0 : ((x.title > y.title) ? 1 : -1 ));
    }

    function SortByAuthor(x,y) {
        return ((x.authors == y.authors) ? 0 : ((x.authors > y.authors) ? 1 : -1 ));
    }
</script>
<asset:javascript src="searchContents.js"/>
<g:render template="/wonderpublish/buyOrAdd"/>
<g:render template="/evidya/storeHolder"/>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<script>
    $('#search-book').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks2(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch();
        }
    });

    $(document).on("keypress", "#search-book", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                if(document.getElementById("search-book").value == '' || document.getElementById("search-book").value == undefined || document.getElementById("search-book").value == null) location.reload();
                else submitSearch();
            }
        }
    });

    function submitSearch(){
        $('.loading-icon').removeClass('hidden');
        var searchString =encodeURIComponent(document.getElementById("search-book").value);
        $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="discover" action="search"  onSuccess='searchResults(data);'
                params="'searchString='+searchString" />
    }

    // function searchResults(data){
    //
    //     if(data.search) searchMode = true;
    //     books = data.books;
    //     booksTags = data.booksTag;
    //     displayBooks("all","");
    // }


    function searchResults(data) {
        if (data.status != "Nothing present") {
            $('.loading-icon').addClass('hidden');
            $("#booksDisplayList").show();
            if (data.search) searchMode = true;
            books = data.books;
            booksTags = data.booksTag;
            displayBooks("all", "");
        } else {
            if (elementExists("searcherrormsg")) {
                $('.loading-icon').addClass('hidden');
                document.getElementById("searcherrormsg").innerText = "No Results Found";
                $("#searcherrormsg").show();
                $("#booksDisplayList").hide();
                $("#paginationList").hide();
                $("#topPaginationMessage").hide();

            }


        }
    }
<%  if("true"==params.search){%>
    searchMode=true;
    getBooksList();
<%  }  %>



    // $(document).ready(function() {
    //     searchMode=false;
    //     alert("Hello, world!");
    //     console.log("inininininiininiready");
    //     getBooksList();
    //
    //
    // });

    if('${params.suggestBookId}'){
        var suggestBookId = '${params.suggestBookId}'
        <g:remoteFunction controller="wonderpublish" action="getBookById"  onSuccess='searchResults(data)'
                params="'id='+suggestBookId" />
    }else{
        getBookCategories();
    }


    $(window).on('load', function() {

        <%if(session['userdetails']!=null&&session['userdetails'].username!=null && session['userdetails'].termsCondition!="true"){%>
            $('#evidyaTCModal').modal('show');
            $('#evidyaTCModal').modal({backdrop: 'static', keyboard: false});
        <%} else { %>
            //do nothing..
        <%}%>
    });
    var termsChecked
    $('#termsConditionCheck').on('change', function() {
        termsChecked = this.checked
        if (termsChecked){
            $("#agreeButtonWrapper").html("<input type='button' id='agree' onclick='acceptTerms()' class='btn btn-lg continue mt-3' value='Continue'>")
            $("#agreeDummy").hide();
        }else{
            $("#agreeButtonWrapper").html("");
            $("#agreeDummy").show();
        }
    });

    function acceptTerms(){
        <g:remoteFunction controller="log" action="storeSageTermsDtl"  onSuccess='closeTermsModal(data)'
                params="'termsCondition='+termsChecked" />
    }

    function closeTermsModal(data){
        if(data.status == "OK"){
            $('#evidyaTCModal').modal('hide');
        }
    }

</script>
