<g:render template="/wonderpublish/buyOrAdd"></g:render>

<style>
@media (max-width:400px){
    #emailSentModal .modal-body p {
        font-size: 15px !important;
    }
    #emailSentModal .modal-body p br {
        display: none;
    }
}
</style>

<div class="modal" id="deleteBook">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header" style="min-height: 50px;">
                <h4 class="modal-title"></h4>

            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <p class="text-center" style="font-size: 18px;" id="remove-msg">Are you sure you want to Remove book from your library?.</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-primary btn-primary1" onclick="javascript:bookDelete();">Yes</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
            </div>

        </div>
    </div>
</div>
<div class="modal" id="emailSentModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal body -->
            <div class="modal-body">
                <p class="text-center mb-0 py-3" style="font-size: 18px;font-family: 'Montserrat',sans-serif;">That's a great choice! Thank you for <br>recommending the book to your Library.</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-sm btn-primary col-4 col-md-3" data-dismiss="modal" style="font-family: 'Montserrat',sans-serif;">Okay</button>
            </div>

        </div>
    </div>
</div>
<script>
    var searchMode=false;
    var getbookId;
    var books ;
    var booksTags;
    var tempBooks=[];

    var indexSubject="${params.subject}";
    var indexGrade="${params.grade}";
    var indexLanguage="${params.language}";
    var sortBy="title";
    var previousMode="";
    var previousModeValue="";
    var myLibraryMode=false;
    var instituteLibraryCalled=false;
    var totalNumberOfBooksPresent=0;
    var numberOfBooksPerPage=10;
    var totalNumberOfPages=0;
    var numberOfPageNumbersPerUnit=10;
    var currentLastPageNumber=0;
    var bookIds=[];
    var storeBooksData;
    var libraryBooksData = "";
    var libraryBooksList=",";
    var libraryAccess="${showLibrary}";
    function goToPageNumber(pageNumber){
        var startIndex = (pageNumber*numberOfBooksPerPage);
        var endIndex = (pageNumber+1)*numberOfBooksPerPage;
        if(endIndex>tempBooks.length) endIndex=tempBooks.length;
         document.getElementById("topPaginationMessage").innerHTML="<span>"+(startIndex+1)+"-"+endIndex+"</span> of <span>"+tempBooks.length+"</span> items";
         displayBooksPagewise(startIndex,endIndex)
    }

    function displayBooksPagewise(startIndex,endIndex){

        $("html, body").animate({ scrollTop: 0 }, "fast");

        $('.loading-icon').removeClass('hidden');
        var htmlStr="<div class=\"book-grid-container\">";
        var language="";
        var imgSrc="";
        // console.log(data);

        for(var i=startIndex;i<endIndex;i++){

            if(tempBooks[i].language==null||""==tempBooks[i].langauge||"null"==tempBooks[i].language) language="English";
            else {
                language=tempBooks[i].language;
            }
            imgSrc = "/funlearn/showProfileImage?id=" + tempBooks[i].id + "&fileName=" + tempBooks[i].coverImage + "&type=books&imgType=passport";

            // Modern card layout
            htmlStr +="<div class=\"book-card-modern\">" +
                "<div class=\"book-cover-wrapper\">";


            if(instituteLibraryCalled||libraryBooksList.indexOf(","+tempBooks[i].id+",")>-1){
                htmlStr +=  "<a class='lib-showcase' href='/library/" + encodeURIComponent(tempBooks[i].title).replace(/'/g,"&#39;") + "?bookId=" + tempBooks[i].id + "&siteName=${session['entryController']}'>" +
                    "<img src='" + imgSrc + "' class=\"book-cover-image-modern\" alt=\"" + tempBooks[i].title + "\">" +
                    "</a>" ;
            }
                else{
                    htmlStr +=  "<img src='" + imgSrc + "' class=\"book-cover-image-modern\" alt=\"" + tempBooks[i].title + "\">";
                    htmlStr +="<div style='position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); border-radius: 4px; padding: 4px;'><img src='/assets/evidya/lock.png' style='width: 20px; height: 20px;'></div>";
                }

            htmlStr += "</div>" + // Close book-cover-wrapper
                "<div class=\"book-details-modern\">" +
                "<h3 class=\"book-title-modern\">" + tempBooks[i].title + "</h3>";

                if(tempBooks[i].authors!=null && tempBooks[i].authors!="") {
                    htmlStr += "<p class=\"book-author-modern\">" + replaceAll(tempBooks[i].authors?tempBooks[i].authors:"", /[-,~]/g, ", ") + "</p>";
                }else{
                    htmlStr += "<p class=\"book-author-modern\">" + (tempBooks[i].authors?tempBooks[i].authors:"") + "</p>";
                }

            htmlStr += "<div class='book-language-tag'>" + language + "</div>";

            // Action buttons container
            htmlStr += "<div style='margin-top: 12px;'>";
            if(libraryAccess!="false" && libraryBooksData.institutionEmail != undefined && libraryBooksData.institutionEmail != null && libraryBooksData.institutionEmail != '' && !(instituteLibraryCalled||libraryBooksList.indexOf(","+tempBooks[i].id+",")>-1)){
                htmlStr += "<a style='display: inline-block; padding: 6px 12px; background: #3499FF; color: white; border-radius: 4px; text-decoration: none; font-size: 12px; margin-top: 8px;' onclick='sendEmail({\"siteName\":\"evidya\"," +
                    "\"toEmail\":\""+libraryBooksData.institutionEmail+"\","+
                    "\"title\":\""+(tempBooks[i].title).replace(/'/g,"&#39;")+"\","+
                    "\"id\":\""+tempBooks[i].id+"\","+
                    "\"coverImage\":\""+tempBooks[i].coverImage+"\","+
                    "\"author\":\""+replaceAll(tempBooks[i].authors?tempBooks[i].authors:"",/[-,~]/g,',')+"\""+
                    "})' " +
                    "href='#'" +
                    " target=\"_top\">Suggest This Book</a>" ;
            }

             if(instituteLibraryCalled&&!bookIds.toString().includes(tempBooks[i].id)) {
                htmlStr += "<a href=\"javascript:addToMyLibrary(" + tempBooks[i].id + "," + tempBooks[i].offerPrice + ",'" + tempBooks[i].title.replace(/'/g,"\\'") + "');\" style='display: inline-block; padding: 6px 12px; background: #28a745; color: white; border-radius: 4px; text-decoration: none; font-size: 12px; margin-top: 8px;'>" +
                    "<i class=\"material-icons\" style='font-size: 14px; margin-right: 4px;'>add_circle_outline</i>Add to Library</a>";
            }
            else if(instituteLibraryCalled&&bookIds.toString().includes(tempBooks[i].id)) {
                htmlStr += "<span style='display: inline-block; padding: 6px 12px; background: #6c757d; color: white; border-radius: 4px; font-size: 12px; margin-top: 8px;'>" +
                    "<i class=\"material-icons\" style='font-size: 14px; margin-right: 4px;'>done_all</i>Added to Library</span>";
            }

            htmlStr += "</div>" + // Close action buttons container
                "</div>" + // Close book-details-modern
                "</div>"; // Close book-card-modern
        }

        htmlStr += "</div>"; // Close book-grid-container

        document.getElementById("booksDisplayList").innerHTML=htmlStr;

        $('.loading-icon').addClass('hidden');
    }

   function displayMyLibraryBooks(books){
        if(bookssearch1.length == 0) bookssearch1 = books;
        $("html, body").animate({ scrollTop: 0 }, "fast");

        $('.loading-icon').removeClass('hidden');
        var htmlStr="<div class=\"book-grid-container\">";
        var language="";
        var imgSrc="";
        for(var i=0;i<books.length;i++){
            bookIds.push(books[i].id);
            if(books[i].language==null||""==books[i].langauge||"null"==books[i].language) language="English";
            else {
                language=books[i].language;
            }
            imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=passport";

            htmlStr +="<div class=\"book-card-modern\">" +
                "<div class=\"book-cover-wrapper\">";

                htmlStr +=  "<a class='lib-showcase' href='/library/" + encodeURIComponent(books[i].title).replace(/'/g,"&#39;").toLowerCase() + "?bookId=" + books[i].id + "&siteName=${session['entryController']}'>" +
                    "<img src='" + imgSrc + "' class=\"book-cover-image-modern\" alt=\"" + books[i].title + "\">" +
                    "</a>" ;

            htmlStr += "</div>" + // Close book-cover-wrapper
                "<div class=\"book-details-modern\">" +
                "<h3 class=\"book-title-modern\">" + books[i].title + "</h3>";

                if(books[i].authors!=null && books[i].authors!=""){
                    htmlStr += "<p class=\"book-author-modern\">" + replaceAll(books[i].authors?books[i].authors:"",/[-,~]/g,", ") + "</p>";
                }else{
                    htmlStr += "<p class=\"book-author-modern\">" + (books[i].authors?books[i].authors:"") + "</p>";
                }

            htmlStr += "<div class='book-language-tag'>" + language + "</div>";

            htmlStr += "<div style='margin-top: 12px;'>" +
                "<a href=\"javascript:deleteModal(" + books[i].id + ");\" style='display: inline-block; padding: 6px 12px; background: #dc3545; color: white; border-radius: 4px; text-decoration: none; font-size: 12px;'>" +
                "<i class=\"material-icons\" style='font-size: 14px; margin-right: 4px;'>remove_circle_outline</i>Remove from Library</a>" +
                "</div>";

            htmlStr += "</div>" + // Close book-details-modern
                "</div>"; // Close book-card-modern
        }

        htmlStr += "</div>"; // Close book-grid-container

        if(tempBooks.length>numberOfBooksPerPage) {
            displayBooksPagewise(0,numberOfBooksPerPage);
        }else{
            displayBooksPagewise(0,tempBooks.length);
        }

            document.getElementById("booksDisplayListMyLibrary").innerHTML=htmlStr;


        $('.loading-icon').addClass('hidden');

    }
    function displayBooks(mode,modeValue,gradeVal="") {
        var searchbook = '';
        if (document.getElementById("search-book") != null) searchbook = document.getElementById("search-book").value;
        if (searchbook == '' && searchMode == true) {
            window.location.href = window.location.href.split('?')[0] + '?mode=' + mode + '&modeValue=' + modeValue;
            if (window.location.href) {
                $('.loading-icon').removeClass('hidden');
            }
        }

        if (!myLibraryMode) {
            $("#bottomPagination").hide();
            $("#topPaginationMessage").hide();
        }

        totalNumberOfBooksPresent = 0;
        tempBooks = [];


        var foundBook = false;

        if (indexSubject) {
            mode = "subject";
            modeValue = indexSubject;
            indexSubject = "";
       } else if (indexGrade) {
            mode = "grade";
            modeValue = indexGrade;
            indexGrade = "";
        } else if (indexLanguage) {
            mode = "language";
            modeValue = indexLanguage;
            indexLanguage = "";
       }
        previousMode = mode;

        // if(books!=null){
            if(elementExists("searcherrormsg")) {
                $("#searcherrormsg").hide();
            }
        previousModeValue = modeValue;
        for (var i = 0; i < books.length; i++) {
            if(elementExists("libraryDesc")) {
                document.getElementById("libraryDesc").innerHTML = " ";
            }

            if ("subject" == mode) {

                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";
                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && booksTags[j].subject == modeValue && booksTags[j].grade==gradeVal) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            } else if ("language" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";

                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && books[i].language == modeValue) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            } else if ("grade" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";

                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && booksTags[j].grade == modeValue) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;

            }

            // all the filtering logic is over. Time to count number of books present
            totalNumberOfBooksPresent++;
            tempBooks.push(books[i]);


        }
    // }
    //     else{
    //         if(elementExists("searcherrormsg")) {
    //             document.getElementById("searcherrormsg").innerText = "No Results Found";
    //             $("#searcherrormsg").show();
    //         }
    //
    //
    //     }


        totalNumberOfPages  =  Math.floor(totalNumberOfBooksPresent/numberOfBooksPerPage);
        if(totalNumberOfBooksPresent%numberOfBooksPerPage>0) totalNumberOfPages++;
        if(myLibraryMode){
            displayBooksPagewise(0,tempBooks.length);

        }
        else{
           if(elementExists("libraryDesc")) {
               if (intest) {
                   document.getElementById("libraryDesc").innerHTML = "Your library now has access to " + totalNumberOfBooksPresent + " titles from evidya store";
               }else{
                   document.getElementById("libraryDesc").innerHTML = " ";
               }
           }

            if(tempBooks.length>numberOfBooksPerPage) {
                $("#bottomPagination").show();
                document.getElementById("topPaginationMessage").innerHTML="<span>1-"+numberOfBooksPerPage+"</span> of <span>"+tempBooks.length+"</span> items";
                $("#topPaginationMessage").show();

                var pageListStr="";
                var pageNumbersToShow=totalNumberOfPages;
                if(totalNumberOfPages>numberOfPageNumbersPerUnit) pageNumbersToShow=numberOfPageNumbersPerUnit;
                for(var index=0;index<pageNumbersToShow;index++){
                    pageListStr +="<li class=\"page-item \"><a class=\"page-link\" href=\"javascript:goToPageNumber("+index+");\">"+(index+1)+"</a></li>";
                }
                if(totalNumberOfPages>numberOfPageNumbersPerUnit) {
                    pageListStr +="<li class='page-item next'><a class='page-link' href='javascript:moveForward(1);'>>></a></li>";
                    currentLastPageNumber=numberOfPageNumbersPerUnit;
                }
                document.getElementById("paginationList").innerHTML=pageListStr;
                displayBooksPagewise(0,numberOfBooksPerPage);
            }else{
                document.getElementById("topPaginationMessage").innerHTML="<span>1-"+tempBooks.length+"</span> of <span>"+tempBooks.length+"</span> items";
                $("#topPaginationMessage").show();
                displayBooksPagewise(0,tempBooks.length);
            }
        }
        if(instituteLibraryCalled){
            <%if(session["userdetails"]!=null){%>
            getUserLibraryBooks();
            <%}%>
        }

    }
    function removeFilter()
    {
        document.getElementById("displayResults").innerHTML="";
        displayBooks("all", "");
    }

    function moveForward(currentIndex){
        var startIndex=(currentIndex)*numberOfPageNumbersPerUnit;
        var endIndex=((currentIndex+1)*numberOfPageNumbersPerUnit);
        var pageListStr="";
        if(endIndex>totalNumberOfPages) endIndex=totalNumberOfPages;
        if(currentIndex>0) pageListStr +="<li class='page-item previous'><a class='page-link' href='javascript:moveBackward("+currentIndex+");'><<</a></li>";
        for(var index=startIndex;index<endIndex;index++){
            pageListStr +="<li class=\"page-item \"><a class=\"page-link\" href=\"javascript:goToPageNumber("+index+");\">"+(index+1)+"</a></li>";
        }
       if(totalNumberOfPages>endIndex){
           currentIndex++;
           pageListStr +="<li class='page-item next'><a class='page-link' href='javascript:moveForward("+currentIndex+");'>>></a></li>";
       }

        document.getElementById("paginationList").innerHTML=pageListStr;

    }

    function moveBackward(currentIndex){
        var startIndex=((currentIndex-1)*numberOfPageNumbersPerUnit);
        var endIndex=((currentIndex)*numberOfPageNumbersPerUnit);
        currentIndex--;
        var pageListStr="";
        if(currentIndex>0) pageListStr +="<li class='page-item previous'><a class='page-link' href='javascript:moveBackward("+currentIndex+");'><<</a></li>";
        for(var index=startIndex;index<endIndex;index++){
            pageListStr +="<li class=\"page-item \"><a class=\"page-link\" href=\"javascript:goToPageNumber("+index+");\">"+(index+1)+"</a></li>";
        }
        if(totalNumberOfPages>endIndex){
            currentIndex++;
            pageListStr +="<li class='page-item next'><a class='page-link' href='javascript:moveForward("+currentIndex+");'>>></a></li>";
        }

        document.getElementById("paginationList").innerHTML=pageListStr;

    }
    var bookssearch=[];
    function booksListReceived(data){
        if(data.status!="Nothing present") {
            if (bookssearch.length == 0) bookssearch = JSON.parse(data.books);
        }
var params = false;
var mode = '';
var modeValue = '';
       if(data.status=="Nothing present") {
           window.location.href="/evidya/store";
        }else {
            books = JSON.parse(data.books);
            booksTags = JSON.parse(data.booksTag);
           <%if("language".equals(params.mode) || "subject".equals(params.mode)){%>
           params = true;
           mode = "${params.mode}";
           modeValue = "${params.modeValue}";
           <%}%>
            if(params){
                displayBooks(mode,modeValue);
            }
           else if(myLibraryMode){
               displayMyLibraryBooks(books);
               document.getElementById("badgeDisplay").innerHTML="<p><img src=\"${assetPath(src: 'evidya/Reader_Badge.svg')}\" class=\"img-responsive\" alt=\"\">"+data.instituteName+"</p>";
           }else {
               if (indexGrade)
                   displayBooks("discipline", indexGrade);
               else displayBooks("all", "");
           }
        }
        myVar = setTimeout(getLibraryBooks, 1000);
    }

    function booksListReceived1(data){
        if(data.status!="Nothing present") {
            if (bookssearch.length == 0) bookssearch = JSON.parse(data.books);
        }
        var params = false;
        var mode = '';
        var modeValue = '';
        if(data.status=="Nothing present") {
            window.location.href="/evidya/store";
        }else {
            books = JSON.parse(data.books);
            booksTags = JSON.parse(data.booksTag);
            <%if("language".equals(params.mode) || "subject".equals(params.mode)){%>
            params = true;
            mode = "${params.mode}";
            modeValue = "${params.modeValue}";
            <%}%>
            if(params){
                displayBooksDisp(mode,modeValue);
            }
            else if(myLibraryMode){
                 displayMyLibraryBooks(books);
                document.getElementById("badgeDisplay").innerHTML="<p><img src=\"${assetPath(src: 'evidya/Reader_Badge.svg')}\" class=\"img-responsive\" alt=\"\">"+data.instituteName+"</p>";
            }else {
                if (indexGrade)
                    displayBooksDisp("discipline", indexGrade);
                else displayBooks("all", "");
            }
        }

    }

    function displayBooksDisp(mode,modeValue,gradeVal="") {
        var searchbook = '';
        if (document.getElementById("search-book") != null) searchbook = document.getElementById("search-book").value;
        if (searchbook == '' && searchMode == true) {
            window.location.href = window.location.href.split('?')[0] + '?mode=' + mode + '&modeValue=' + modeValue;
            if (window.location.href) {
                $('.loading-icon').removeClass('hidden');
            }
        }

        if (!myLibraryMode) {
            $("#bottomPagination").hide();
            $("#topPaginationMessage").hide();
        }

        totalNumberOfBooksPresent = 0;
        tempBooks = [];


        var foundBook = false;

        if (indexSubject) {
            mode = "subject";
            modeValue = indexSubject;
            indexSubject = "";
        } else if (indexGrade) {
            mode = "grade";
            modeValue = indexGrade;
            indexGrade = "";
        } else if (indexLanguage) {
            mode = "language";
            modeValue = indexLanguage;
            indexLanguage = "";
        }
        previousMode = mode;

        // if(books!=null){
        if(elementExists("searcherrormsg")) {
            $("#searcherrormsg").hide();
        }
        previousModeValue = modeValue;
        for (var i = 0; i < books.length; i++) {
            if(elementExists("libraryDesc")) {
                document.getElementById("libraryDesc").innerHTML = " ";
            }
            if ("subject" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";
                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && booksTags[j].subject == modeValue && booksTags[j].grade == gradeVal && libraryBooksList.indexOf(","+books[i].id+",")>-1) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            } else if ("language" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";

                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && books[i].language == modeValue &&  libraryBooksList.indexOf(","+books[i].id+",")>-1) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            } else if ("grade" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";

                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && booksTags[j].grade == modeValue && libraryBooksList.indexOf(","+books[i].id+",")>-1) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;

            }
            // all the filtering logic is over. Time to count number of books present
            totalNumberOfBooksPresent++;
            tempBooks.push(books[i]);
        }

        for (var i = 0; i < books.length; i++) {
            if(elementExists("libraryDesc")) {
                document.getElementById("libraryDesc").innerHTML = " ";
            }
            if ("subject" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";
                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && booksTags[j].subject == modeValue && booksTags[j].grade == gradeVal && libraryBooksList.indexOf(","+books[i].id+",")<0) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            } else if ("language" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";

                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && books[i].language == modeValue && libraryBooksList.indexOf(","+books[i].id+",")<0) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            } else if ("grade" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";

                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && booksTags[j].grade == modeValue && libraryBooksList.indexOf(","+books[i].id+",")<0) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;

            }
            // all the filtering logic is over. Time to count number of books present
            totalNumberOfBooksPresent++;
            tempBooks.push(books[i]);
        }
        totalNumberOfPages  =  Math.floor(totalNumberOfBooksPresent/numberOfBooksPerPage);
        if(totalNumberOfBooksPresent%numberOfBooksPerPage>0) totalNumberOfPages++;
        if(myLibraryMode){
            displayBooksPagewise(0,tempBooks.length);

        }
        else{
            if(elementExists("libraryDesc")) {
                if (intest) {
                    document.getElementById("libraryDesc").innerHTML = "Your library now has access to " + totalNumberOfBooksPresent + " titles from evidya store";
                }else{
                    document.getElementById("libraryDesc").innerHTML = " ";
                }
            }

            if(tempBooks.length>numberOfBooksPerPage) {
                $("#bottomPagination").show();
                document.getElementById("topPaginationMessage").innerHTML="<span>1-"+numberOfBooksPerPage+"</span> of <span>"+tempBooks.length+"</span> items";
                $("#topPaginationMessage").show();

                var pageListStr="";
                var pageNumbersToShow=totalNumberOfPages;
                if(totalNumberOfPages>numberOfPageNumbersPerUnit) pageNumbersToShow=numberOfPageNumbersPerUnit;
                for(var index=0;index<pageNumbersToShow;index++){
                    pageListStr +="<li class=\"page-item \"><a class=\"page-link\" href=\"javascript:goToPageNumber("+index+");\">"+(index+1)+"</a></li>";
                }
                if(totalNumberOfPages>numberOfPageNumbersPerUnit) {
                    pageListStr +="<li class='page-item next'><a class='page-link' href='javascript:moveForward(1);'>>></a></li>";
                    currentLastPageNumber=numberOfPageNumbersPerUnit;
                }
                document.getElementById("paginationList").innerHTML=pageListStr;
                displayBooksPagewise(0,numberOfBooksPerPage);
            }else{
                document.getElementById("topPaginationMessage").innerHTML="<span>1-"+tempBooks.length+"</span> of <span>"+tempBooks.length+"</span> items";
                $("#topPaginationMessage").show();
                displayBooksPagewise(0,tempBooks.length);
            }
        }
        if(instituteLibraryCalled){
            <%if(session["userdetails"]!=null){%>
            getUserLibraryBooks();
            <%}%>
        }

    }



    function sendEmail(data){
       var url = $.param( data );
        <g:remoteFunction controller="creation" action="sendRecomentBook" params="url" onSuccess = "emailSent(data);"/>
    }

    function emailSent(data) {
        $("#emailSentModal").modal("show");
       //alert("This book is suggested to the library!");
    }
</script>
<script>
        var evidyaUrl = location.href;
        if (evidyaUrl.indexOf('grade=Business') >= 0) {
            $('#management').removeClass('minus');
            $('#fiction').addClass('minus');
            $('#social').addClass('minus');
            $('#upsctoggle').addClass('minus');
            $('#SocialScience').hide();
            $('#NonFiction').hide();
            $('#UPSC').hide();

            $('#spectrumToggle').addClass('minus')
            $('#spectrum').hide()
        }
        else if(evidyaUrl.indexOf('grade=Social%20Science') >= 0){
            $('#social').removeClass('minus');
            $('#management').addClass('minus');
            $('#fiction').addClass('minus');
            $('#upsctoggle').addClass('minus');
            $('#Business').hide();
            $('#NonFiction').hide();
            $('#UPSC').hide();

            $('#spectrumToggle').addClass('minus')
            $('#spectrum').hide()
        }else if(evidyaUrl.indexOf('grade=Non-Fiction') >= 0){
            $('#social').addClass('minus');
            $('#management').addClass('minus');
            $('#fiction').removeClass('minus');
            $('#upsctoggle').removeClass('minus');
            $('#Business').hide();
            $('#SocialScience').hide();
            $('#UPSC').hide();

            $('#spectrumToggle').addClass('minus')
            $('#spectrum').hide()
        }else if(evidyaUrl.indexOf('grade=UPSC') >= 0){
            $('#social').addClass('minus');
            $('#management').addClass('minus');
            $('#fiction').addClass('minus');
            $('#upsctoggle').removeClass('minus');
            $('#Business').hide();
            $('#SocialScience').hide();
            $('#NonFiction').hide();

            $('#spectrumToggle').addClass('minus')
            $('#spectrum').hide()
        }else if(evidyaUrl.indexOf('grade=SAGE%20Spectrum') >= 0){
            $('#social').addClass('minus');
            $('#management').addClass('minus');
            $('#fiction').addClass('minus');
            $('#upsctoggle').addClass('minus');
            $('#Business').hide();
            $('#SocialScience').hide();
            $('#NonFiction').hide();
            $('#UPSC').hide();

            $('#spectrumToggle').removeClass('minus')
            $('#spectrum').show()
        }

</script>
