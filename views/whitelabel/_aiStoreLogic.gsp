<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "url": "${request.getRequestURL()}",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://${request.getServerName()+storeUrl}?searchString={search_term_string}",
    "query-input": "required name=search_term_string"
  }
}
</script>
<%if(publisherDescription!=null){%>
<script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "${publisherName}",
            "url": "${request.getRequestURL()}",
            "description": "${publisherDescription}"
          }
    </script>
<%}%>
<script>

    <%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())
      %>

    function shareBooks(){
        var booksUrl = "https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.href);
        openShareContentModal("ebooks", booksUrl);
    }
</script>
<script>
    <%
        String requestUrl = ""+request.getRequestURL()
        String queryString =  request.getQueryString()%>
    <%    if(requestUrl.indexOf("wonderslate.com")==-1&&requestUrl.indexOf("prepjoy.com")==-1&&requestUrl.indexOf("/sp/")>-1&&queryString==null){
    %>
    var base_url = location.protocol + '//' + location.host;
    window.history.replaceState("", "", base_url);
    <%}%>
    var tokenId="${params.tokenId}";
    var allBooksData;
    var useSearchSelect = false;
    var pageNo;
    var isAjaxCalled;
    var allBooksLoaded = false;
    var showPublishers = false;
    var searchMode = false;
    var searchStatus = "";
    var pageInitialized = false;
    var siteName="";
    var prepjoySite = "${session['prepjoySite']}";
    var subscriptionBooks=false;
    var amazonSearch = false;
    var wiley=false;
    var wileyCollectionBookId=-1;
    <%if("66".equals(""+session["siteId"])){%>
    wiley=true;
    <%}%>
    <%if(session['wileyCollectionBookId']!=null){%>
    wileyCollectionBookId = ${session['wileyCollectionBookId']};
    <%}%>

    var ebookOfTheDay,newlyReleasedEbook,trendingNowEbook,urltags = "";

    var disablePrev = 1;

    <%if("true".equals(session["prepjoySite"]) || "true".equals(session["commonWhiteLabel"])){%>
    siteName="${session['siteName']}";
    <%}else{%>
    siteName="${session['entryController']}";
    <% }%>

    <%if(showPublishers!=null&&"true".equals(showPublishers)){%>
        showPublishers = true;
   <% }%>

    // Enhanced search functionality for AI Store
    function submitSearchTop(){
        isAjaxCalled = true;
        var searchString = document.getElementById("search-book-store").value;
        var callRemoteSearch = true;

        if (!prepjoySite){
            document.querySelector('.loading-icon').classList.remove('hidden');
        }else{
            document.getElementById('loading').style.display = 'block';
        }

        document.getElementById("content-data-books-ebooks").innerHTML="";
        if(searchStringValues!=null&&searchStringValues!=""){
            for(var i=0;i<searchStringValues.length;i++){
                if(searchStringValues[i]==searchString){
                    callRemoteSearch = populateFromFilters(searchDataValues[i]);
                    break
                }
            }
        }
        if(callRemoteSearch) {
            var level=null,syllabus=null,grade=null,publisherId=null,subject=null,pageNo;
           <% if(params.publisherId!=null||params.level!=null||params.syllabus!=null||params.grade!=null||params.subject!=null) {%>
            pageInitialize();
            <%}else {%>
                <g:remoteFunction controller="discover" action="search"  onSuccess='booksReceived(data);'
         params="'searchString='+searchString" />
            <%}%>
        }
    }

    function populateFromFilters(searchData){
        var level=null,syllabus=null,grade=null,publisherId=null,subject=null,pageNo;
        if(searchData.hasOwnProperty("publisherId")||searchData.hasOwnProperty("level")||searchData.hasOwnProperty("syllabus")||searchData.hasOwnProperty("grade")||searchData.hasOwnProperty("subject")){
            if(searchData.hasOwnProperty("publisherId")){
                publisherId = searchData.publisherId;
            }
            if(searchData.hasOwnProperty("level")){
                level = searchData.level;
            }
            if(searchData.hasOwnProperty("syllabus")){
                syllabus = searchData.syllabus;
            }
            if(searchData.hasOwnProperty("grade")){
                grade = searchData.grade;
            }
            if(searchData.hasOwnProperty("subject")){
                subject = searchData.subject;
            }
            setBlankState();
            getBooksWithParameters(level,syllabus,grade,subject,publisherId,0);
        }
        else{
            return true
        }
    }

    function replaceAll(str, find, replace) {
        if(str==undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }
    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }

    function booksReceived(data){
        searchStatus = "";
        if(data.status=="Nothing present"){
            document.getElementById("load-more-button").style.display = 'none';
            searchStatus = data.status;

            if (!prepjoySite){
                document.querySelector('.loading-icon').classList.add('hidden');
            } else {
                document.getElementById('loading').style.display = 'none';
            }
            searchMode=true;
            pageNo = -1;
            allBooksLoaded = true;
            setBlankState();
            getBooks();

            document.getElementById("resetFilter").removeAttribute('disabled');
            document.getElementById("resetFilter").style.color = '#007bff';
            var searchStringNotFound = document.getElementById("search-book-store").value;
          if ("${session['siteId']}" == 27 || "${session['siteId']}" == 1 ){
              document.getElementById("amazonBooksTitle").innerHTML = "";
              document.getElementById("amazonBooksTitle").style.display = "none";
              amazonSearch = true;
            <g:remoteFunction controller="affiliation" action="amazonSearch"  onSuccess='amazonSearchResults(data);'
                  params="'searchKey='+searchStringNotFound" />
          }

        }else {
            document.getElementById("content-data-books-ebooks").innerHTML = "";
            allBooksData = data;
            var books = JSON.parse(JSON.stringify(data.books));
            if(data.booksTag.length>0){
                var bookTag = data.booksTag[0];
                setBlankState();
                displayBooks(books);
            }
            else {
                displayBooks(books);
                searchMode=true;
                pageNo = -1;
                allBooksLoaded = true;
            }
            document.getElementById("resetFilter").removeAttribute('disabled');
            document.getElementById("resetFilter").style.color = '#007bff';
        }
    }

    // Enhanced displayBooks function for grid layout
    function displayBooks(books){
        var colors=['#667eea','#764ba2','#f093fb','#f5576c','#4facfe','#00f2fe','#43e97b','#38f9d7','#ffecd2','#fcb69f','#a8edea','#fed6e3'];
        var noOfEbooks  = 0;
        var ebookHtmlStr="";
        var imgSrc = "";
        var bookUrl="";
        var offerPrice = "";
        var listPrice = "";
        var addToCartBtn = "";
        var bookArray = [];
        
        for(var i=0; i<books.length;i++){
            if(bookArray.includes(books[i].id)) continue;
            else bookArray.push(books[i].id);
            
            // Price calculation logic (same as original)
            if (books[i].offerPrice || (books[i].testsPrice && books[i].offerPrice)){
                if (books[i].offerPrice == 0 || books[i].offerPrice == 0.0 || books[i].offerPrice == null) {
                    offerPrice = "<span class='badge bg-success'>FREE</span>";
                    addToCartBtn = "";
                } else {
                    offerPrice = "₹" + books[i].offerPrice;
                }
                if (books[i].listPrice == 0 || books[i].listPrice == 0.0 || books[i].listPrice == "null" || books[i].listPrice == null || books[i].listPrice == books[i].offerPrice) {
                    listPrice = "";
                } else {
                    listPrice = "₹" + books[i].listPrice;
                }
            }else if(books[i].testsPrice){
                if (books[i].testsPrice == 0 || books[i].testsPrice == 0.0 || books[i].testsPrice == null) {
                    offerPrice = "<span class='badge bg-success'>FREE</span>";
                    addToCartBtn = "";
                } else {
                    offerPrice = "₹" + books[i].testsPrice;
                }
                if (books[i].testsListprice == 0 || books[i].testsListprice == 0.0 || books[i].testsListprice == "null" || books[i].testsListprice == null || books[i].testsListprice == books[i].testsPrice) {
                    listPrice = "";
                } else {
                    listPrice = "₹" + books[i].testsListprice;
                }
            }else if(books[i].bookgptSellPrice){
                if (books[i].bookgptSellPrice == 0 || books[i].bookgptSellPrice == 0.0 || books[i].bookgptSellPrice == null) {
                    offerPrice = "<span class='badge bg-success'>FREE</span>";
                    addToCartBtn = "";
                } else {
                    offerPrice = "₹" + books[i].bookgptSellPrice;
                }
                if (books[i].bookgptListPrice == 0 || books[i].bookgptListPrice == 0.0 || books[i].bookgptListPrice == "null" || books[i].bookgptListPrice == null || books[i].bookgptListPrice == books[i].bookgptSellPrice) {
                    listPrice = "";
                } else {
                    listPrice = "₹" + books[i].bookgptListPrice;
                }
            }

            // Image source logic
            imgSrc = books[i].coverImage;
            if (books[i].coverImage!=null && books[i].coverImage.startsWith("https")) {
                imgSrc = books[i].coverImage;
                imgSrc = imgSrc.replace("~", ":");
            } else {
                imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=passport";
            }

            // Calculate discount percentage
            var percentageVal = 0;
            if (books[i].listPrice !=0 && books[i].listPrice !=null ){
                var offerPricePer = books[i].offerPrice;
                var actualPricePer = books[i].listPrice;
                var percentageVal = (actualPricePer - offerPricePer);
                percentageVal =(percentageVal * 100 / actualPricePer).toFixed(0);
            }else if (books[i].bookgptListPrice !=0 && books[i].bookgptListPrice !=null ){
                var offerPricePer = books[i].bookgptSellPrice;
                var actualPricePer = books[i].bookgptListPrice;
                var percentageVal = (actualPricePer - offerPricePer);
                percentageVal =(percentageVal * 100 / actualPricePer).toFixed(0);
            }

            // Build the modern card HTML
            ebookHtmlStr += "<div class='ai-book-card'>";
            
            // Image container
            ebookHtmlStr += "<div class='ai-book-image-container'>";
            
            // Book link
            if(books[i].testTypeBook=="true") {
                ebookHtmlStr += "<a href='/wpmain/aiBookDtl?bookId=" + books[i].id +"'>";
            }else{
            <% if(session['appInApp']==null){%>
                ebookHtmlStr += "<a href='/" + replaceAll(books[i].title,' ','-') +"/ebook-details?siteName="+siteName+"&bookId=" + books[i].id +"&publisher="+replaceAll(books[i].publisher,' ','-')+"&preview=true' target='_blank'>";
           <%  }else{%>
                ebookHtmlStr += "<a href='/" + replaceAll(books[i].title,' ','-')+"/ebook-details?siteName="+siteName+"&bookId=" + books[i].id + "&preview=true'>";
           <% }%>
            }
            
            // Book image or dummy cover
            if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                var isIbookGPTPublisher = false;
                if(books[i].publisher == "iBookGPT"){
                    isIbookGPTPublisher = true;
                }
                ebookHtmlStr += createCover(books[i].grade, books[i].title, books[i].subject, books[i].level, books[i].syllabus, isIbookGPTPublisher);
            }
            else {
                ebookHtmlStr += "<img src='" + imgSrc + "' alt='Book Cover Image' class='ai-book-image'/>" ;
            }

            // Discount badge
            if (percentageVal > 0) {
                ebookHtmlStr += "<div class='ai-book-badge'>" + percentageVal + "% OFF</div>";
            }

            ebookHtmlStr += "</a></div>";

            // Book content
            ebookHtmlStr += "<div class='ai-book-content'>";

            // Title
            if(books[i].testTypeBook=="true") {
                ebookHtmlStr += "<a href='/wpmain/aiBookDtl?bookId=" + books[i].id +"' class='text-decoration-none'>";
            } else if(tokenId=="") {
                ebookHtmlStr += "<a href='/" + replaceAll(books[i].title,' ','-') + "/ebook-details?siteName="+siteName+"&bookId=" + books[i].id +"&publisher="+replaceAll(books[i].publisher,' ','-')+"&preview=true' target='_blank' class='text-decoration-none'>";
            }else{
                ebookHtmlStr += "<a href='/" + replaceAll(books[i].title,' ','-') +"/ebook-details?siteName="+siteName+"&bookId=" + books[i].id + "&preview=true&tokenId=" + tokenId + "' class='text-decoration-none'>";
            }
            ebookHtmlStr += "<h3 class='ai-book-title'>"+ books[i].title + "</h3></a>";

            // Publisher
            ebookHtmlStr += "<p class='ai-book-publisher'>By " + books[i].publisher + "</p>";

            // ISBN
            if (books[i].isbn != null && books[i].isbn != "") {
                ebookHtmlStr += "<p class='ai-book-isbn'>ISBN: "+books[i].isbn+"</p>";
            }

            // Price and actions (only for non-print books)
            if (!(books[i].bookType == "print"||subscriptionBooks||(wiley&&wileyCollectionBookId!=books[i].id))) {
                ebookHtmlStr += "<div class='ai-book-price-container'>";
                ebookHtmlStr += "<div class='ai-book-price'>";
                if(listPrice) {
                    ebookHtmlStr += "<span class='ai-book-original-price'>" + listPrice + "</span>";
                }
                ebookHtmlStr += offerPrice;
                if (books[i].validityDays == "365") {
                    ebookHtmlStr += "<small class='text-muted ms-1'>for 1 year</small>";
                }
                ebookHtmlStr += "</div></div>";

                // Action buttons
                ebookHtmlStr += "<div class='ai-book-actions'>";
                if(books[i].bookType === "bookgpt" || books[i].bookType ==="ebookwithai"){
                    if(books[i].testTypeBook=="true")
                        ebookHtmlStr +="<a href='/wpmain/aibook?siteName="+siteName+"&bookId="+books[i].id+"' class='ai-btn-primary'><i class='material-icons' style='font-size: 14px;'>smart_toy</i> Try AI</a>";
                    else
                        ebookHtmlStr +="<a href='/prompt/bookgpt?siteName="+siteName+"&bookId="+books[i].id+"' class='ai-btn-primary'><i class='material-icons' style='font-size: 14px;'>smart_toy</i> Try AI</a>";
                }else if(books[i].printOnly == false){
                    ebookHtmlStr +="<a href='/" + replaceAll(books[i].title,' ','-') + "/ebook?siteName="+siteName+"&bookId=" + books[i].id+"&preview=true' class='ai-btn-primary'><i class='material-icons' style='font-size: 14px;'>preview</i> Preview</a>";
                }
                ebookHtmlStr += "</div>";
            }

            ebookHtmlStr += "</div></div>";

            noOfEbooks++;
        }

        // Add all cards to the grid
        document.getElementById("content-data-books-ebooks").innerHTML += ebookHtmlStr;

        // Handle display logic
        if(noOfEbooks>0)   {
            if(searchMode){
                document.getElementById("load-more-button").style.display = "none";
                searchMode = false;
                if(searchStatus=="Nothing present"){
                    if ("${session['siteId']}" == 27 || "${session['siteId']}" == 1 ) {
                        if(!amazonSearch){
                            document.getElementById("amazonBooksTitle").innerHTML = "";
                            document.getElementById("amazonBooksTitle").style.display = "none";
                            document.getElementById("noResultsFound").style.display = "none";
                        }
                        amazonSearch = false;
                    } else {
                        document.getElementById("noResultsFound").style.display = "block";
                    }
                    document.getElementById("load-more-button").style.display = "none";
                }
            }
            else{
                document.getElementById("amazonBooksTitle").innerHTML = "";
                document.getElementById("amazonBooksTitle").style.display = "none";
                document.getElementById("noResultsFound").style.display = "none";
                if(noOfEbooks>=20)   {
                    document.getElementById("load-more-button").style.display = "block";
                    document.getElementById("view-more").innerHTML = "<i class=\"material-icons me-2\" style=\"font-size: 18px;\">expand_more</i>Load More Books";
                } else {
                    subscriptionBooks=false;
                    document.getElementById("load-more-button").style.display = "none";
                }
            }
        } else {
            document.getElementById("load-more-button").style.display = "none";
            document.getElementById("content-data-books-ebooks").innerHTML = "<div class='ai-no-results'>" +
                "<div class='ai-no-results-icon'><i class='material-icons'>sentiment_dissatisfied</i></div>" +
                "<div class='ai-no-results-title'>No Books Found</div>" +
                "<div class='ai-no-results-text'>We couldn't find any books matching your criteria. Please try clearing filters or adjusting your search.</div>" +
                "<button onclick='resetFilters()' class='ai-clear-filters mt-3'>Clear All Filters</button>" +
                "</div>";
        }

        if (!prepjoySite){
            document.querySelector('.loading-icon').classList.add('hidden');
        }else {
            document.getElementById('loading').style.display = 'none';
        }

        // Apply colors to dummy covers
        var fronts = document.querySelectorAll(".uncover");
        for(var i=0 ; i < fronts.length; i++) {
            fronts[i].style.background = colors[i%12];
        }

        // Add loading effect for links
        var links = document.querySelectorAll("#content-data-books-ebooks a");
        links.forEach(function(link) {
            link.addEventListener("click", function() {
                if (!prepjoySite){
                    document.querySelector('.loading-icon').classList.remove('hidden');
                }else {
                    document.getElementById('loading').style.display = 'block';
                }
            });
        });
    }

    // Rest of the original logic functions
    pageNo = 0;
    isAjaxCalled = false;
    window.addEventListener("load", function () {
        document.getElementById("view-more").addEventListener("click", function() {
            this.innerHTML = "<i class=\"material-icons me-2\" style=\"font-size: 18px;\">hourglass_empty</i>Loading...";
            getBooks();
        });
    });

    var prepJoySearchBook=false;
    var catalogueLevel=null, catalogueSyllabus=null, catalogueGrade=null, catalogueSubject=null, cataloguePublisher=null;

    function getBooksWithParameters(level,syllabus,grade,subject,publisherId,pageNo){
        level =replaceAll(level,'-',' ');
        syllabus = replaceAll(syllabus,'-',' ');
        grade = replaceAll(grade,'-',' ');
        subject = replaceAll(subject,'-',' ');

       if (!prepjoySite){
           document.querySelector('.loading-icon').classList.remove('hidden');
       }else{
           document.getElementById('loading').style.display = 'block';
       }

        var title="";
        var pageUrl = window.location.href;
        if (pageUrl.indexOf("?") > 0) {
            pageUrl = pageUrl.substr(0, pageUrl.indexOf("?"));
        }
        pageUrl+="?linkSource=store&";

        if(level!=null&&level!=""&&level!="null"){
            catalogueLevel = level;
            pageUrl+="level="+replaceAll(decodeURI(level),' ','-')+"&";
            document.getElementById("resetFilter").removeAttribute('disabled');
            document.getElementById("resetFilter").style.color = '#007bff';
        }
        if(syllabus!=null&&syllabus!=""&&syllabus!="null"){
            catalogueSyllabus = syllabus;
            pageUrl+="syllabus="+replaceAll(decodeURI(syllabus),' ','-')+"&";
            title +=syllabus+" "
        }
        if(grade!=null&&grade!=""&&grade!="null"&&grade!=""){
            catalogueGrade = grade;
            pageUrl+="grade="+replaceAll(decodeURI(grade),' ','-')+"&";
            title +=grade+" "
        }
        if(subject!=null&&subject!=""&&subject!="null"){
            catalogueSubject = subject;
            pageUrl+="subject="+replaceAll(decodeURI(subject),' ','-')+"&";
        }

        if(publisherId!=null&&publisherId!=""&&publisherId!="null"){
            document.getElementById("resetFilter").removeAttribute('disabled');
            document.getElementById("resetFilter").style.color = '#007bff';
            cataloguePublisher = publisherId;
            if(showPublishers&&document.getElementById("publisher").selectedIndex>0){
                    var publisherName = document.getElementById("publisher")[document.getElementById("publisher").selectedIndex].label;
                    publisherName = publisherName.split(" ").join("-");
                    pageUrl += "publisher=" + publisherName
            }
            else{
                pageUrl+="publisherId="+publisherId;
            }
        }

       if(pageInitialized) window.history.replaceState(title + " eBooks on - Wonderslate", "title", pageUrl);
       else pageInitialized = true;

        <g:remoteFunction controller="wonderpublish" action="getNewBooksList"  onSuccess='latestBooksReceived(data);'
                  params="'fromApp=false&categories=true&level='+level+'&syllabus='+syllabus+'&grade='+grade+'&subject='+subject+'&pageNo='+pageNo+'&publisherId='+publisherId+'&getSubscription='+subscriptionBooks" />
    }

    function getBooks(){
        pageNo = pageNo+1;
        isAjaxCalled = true;
        var level = encodeURIComponent(document.getElementById("level")[document.getElementById("level").selectedIndex].value);
        var syllabus = encodeURIComponent(document.getElementById("syllabus")[document.getElementById("syllabus").selectedIndex].value);
        var grade = encodeURIComponent(document.getElementById("grade")[document.getElementById("grade").selectedIndex].value);
        var subject = encodeURIComponent(document.getElementById("subject")[document.getElementById("subject").selectedIndex].value);
        var publisherId = null;
        console.log("level="+level);
        var element =  document.getElementById('publisher');
        if (showPublishers)
        {
            publisherId = encodeURIComponent(document.getElementById("publisher")[document.getElementById("publisher").selectedIndex].value);
        }else{
            <%if(session["publisherLogo"]!=null){%>
            publisherId = ${session["publisherLogo"]};
            <%}%>
        }

        getBooksWithParameters(level,syllabus,grade,subject,publisherId,pageNo);
    }

    function compareNumbers(a, b) {
        return a - b;
    }

    function latestBooksReceived(data) {
        var books = JSON.parse(data.books);
        var booksPresent = true;

        if(books == '' || books == null || books == 'null' || data.books == '[]') booksPresent = false;

        if("institute"=="${params.source}"&&books.length==0){
            getBooksWithParameters(null,null,null,null,null,0);
        }else {
            if (booksPresent) {
                document.getElementById('filters').classList.remove('d-none');
                document.querySelector('.searching-book-store').classList.remove('d-none');
                document.getElementById('filters').classList.add('d-flex');
                document.querySelector('.searching-book-store').classList.add('d-flex');

                var levelSelect = document.getElementById("level");
                var syllabusSelect = document.getElementById("syllabus");
                var gradeSelect = document.getElementById("grade");
                var subjectSelect = document.getElementById("subject");

                <%if(!"true".equals(session["doNotChangeLabel"])){%>
                syllabusSelect.options[0].innerHTML = "Select Board";
                gradeSelect.options[0].innerHTML = "Select Grade";
                subjectSelect.options[0].innerHTML = "Select Subject";
                <%}%>

                var checkLevel = false, checkSyllabus = false, checkGrade = false;

                var levelTags = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
                var syllabusTags = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
                var gradeTags = [];
                var subjectTags = [];

                var tagsList = JSON.parse(data.bookTags);
                if (data.publisherId != null && "undefined" == data.publisherId) {
                }
                var levels = [];
                if (!(data.publisherId == ""||data.publisherId=="null")) {
                    levelTags = JSON.parse(data.bookTags);
                    syllabusTags = JSON.parse(data.bookTags);
                }
                gradeTags = JSON.parse(data.bookTags);
                subjectTags = JSON.parse(data.bookTags);

                for (var i = 0; i < levelTags.length; i++) {
                    if (levels.indexOf(levelTags[i].level) == -1) levels.push(levelTags[i].level);
                }
                levels.sort();

                // Reset and populate level dropdown
                var select = document.getElementById("level");
                select.options.length = 1;
                for (var i = 0; i < levels.length; i++) {
                    var el = document.createElement("option");
                    el.textContent = levels[i];
                    el.value = levels[i];
                    if (data.level!=null&&levels[i] == data.level.replace("&amp;","&")) {
                        el.selected = true;
                        document.getElementById("level").classList.add("background-bg");
                        document.getElementById("syllabus").style.display = 'block';
                        pageNo = 0;
                        allBooksLoaded = false;
                    }
                    select.appendChild(el);
                }

                var selectedLevel = document.getElementById("level")[document.getElementById("level").selectedIndex].value;
                if (document.getElementById("level").selectedIndex > 0) checkLevel = true

                // Populate syllabus dropdown
                var syllabus = [];
                for (var i = 0; i < syllabusTags.length; i++) {
                    if (checkLevel) {
                        if (selectedLevel == syllabusTags[i].level && syllabus.indexOf(syllabusTags[i].syllabus) == -1) syllabus.push(syllabusTags[i].syllabus);
                    } else if (syllabus.indexOf(syllabusTags[i].syllabus) == -1) {
                        syllabus.push(syllabusTags[i].syllabus);
                    }
                }
                syllabus.sort();

                select = document.getElementById("syllabus");
                select.options.length = 1;
                for (var i = 0; i < syllabus.length; i++) {
                    var el = document.createElement("option");
                    el.textContent = syllabus[i];
                    el.value = syllabus[i];
                    if (data.syllabus!=null&&syllabus[i] == data.syllabus.replace("&amp;","&")) {
                        el.selected = true;
                        document.getElementById("syllabus").classList.add("background-bg");
                        document.getElementById("grade").style.display = 'block';
                        pageNo = 0;
                        allBooksLoaded = false;
                    }
                    select.appendChild(el);
                }

                var selectedSyllabus = document.getElementById("syllabus")[document.getElementById("syllabus").selectedIndex].value;
                if (document.getElementById("syllabus").selectedIndex > 0) checkSyllabus = true

                // Populate grade dropdown
                if(gradeTags.length>0) {
                    var grade = [];
                    for (var i = 0; i < gradeTags.length; i++) {
                        if (grade.indexOf(gradeTags[i].grade) == -1) {
                            grade.push(gradeTags[i].grade);
                        }
                    }

                    if (data.level == 'School') {
                        grade.sort(compareNumbers);
                    } else {
                        grade.sort();
                    }

                    select = document.getElementById("grade");
                    select.options.length = 1;
                    for (var i = 0; i < grade.length; i++) {
                        var el = document.createElement("option");
                        el.textContent = grade[i];
                        el.value = grade[i];
                        if (grade[i] == data.grade) {
                            el.selected = true;
                            document.getElementById("grade").classList.add("background-bg");
                            document.getElementById("subject").style.display = 'block';
                        }
                        select.appendChild(el);
                    }
                    <%if(!session['wileySite'] ){%> document.getElementById("grade").style.display = 'block';<%}%>
                }else{
                    document.getElementById("grade").style.display = 'none';
                }

                // Populate subject dropdown
                if(subjectTags.length>0&&subjectTags[0].subject!='') {
                    var selectedGrade = document.getElementById("grade")[document.getElementById("grade").selectedIndex].value;
                    if (document.getElementById("grade").selectedIndex > 0) checkGrade = true

                    var subject = [];
                    for (var i = 0; i < subjectTags.length; i++) {
                       if(subject.indexOf(subjectTags[i].subject) == -1) subject.push(subjectTags[i].subject);
                    }
                    subject.sort();

                    select = document.getElementById("subject");
                    select.options.length = 1;
                    for (var i = 0; i < subject.length; i++) {
                        var el = document.createElement("option");
                        el.textContent = subject[i];
                        el.value = subject[i];
                        if (subject[i] == data.subject) {
                            el.selected = true;
                            document.getElementById("subject").classList.add("background-bg");
                        }
                        select.appendChild(el);
                    }
                    <%if(!session['wileySite'] ){%> document.getElementById("subject").style.display = 'block';<%}%>
                }else{
                    document.getElementById("subject").style.display = 'none';
                }

                // Populate publisher dropdown
                if (pageNo == 0 && showPublishers) {
                    var publishers = JSON.parse(data.publishers);
                    select = document.getElementById("publisher");
                    select.options.length = 1;

                    for (var i = 0; i < publishers.length; i++) {
                        var el = document.createElement("option");
                        el.textContent = publishers[i].publisher;
                        el.value = publishers[i].publisherId;
                        if (publishers[i].publisherId == data.publisherId) {
                            el.selected = true;
                            document.getElementById("publisher").classList.add("background-bg");
                            allBooksLoaded = false;
                        }
                        select.appendChild(el);
                    }
                }

                // Update dropdown labels based on level selection
                <%if(!"true".equals(session["doNotChangeLabel"])){%>
                if (levelSelect.selectedIndex > 0) {
                    if ("Government Recruitments" == levelSelect[levelSelect.selectedIndex].value) {
                        syllabusSelect.options[0].innerHTML = "Select Level";
                        if ("National Level" == syllabusSelect[syllabusSelect.selectedIndex].value) {
                            gradeSelect.options[0].innerHTML = "Select Exam";
                        } else if ("State Level" == syllabusSelect[syllabusSelect.selectedIndex].value) {
                            gradeSelect.options[0].innerHTML = "Select State";
                            subjectSelect.options[0].innerHTML = "Select Exam";
                        }
                    } else if ("Engineering Entrances" == levelSelect[levelSelect.selectedIndex].value || "Medical Entrances" == levelSelect[levelSelect.selectedIndex].value) {
                        syllabusSelect.options[0].innerHTML = "Select Exam";
                        gradeSelect.options[0].innerHTML = "Select Exam";
                    } else if ("Competitive Exams" == levelSelect[levelSelect.selectedIndex].value) {
                        syllabusSelect.options[0].innerHTML = "Select Exam";
                        if ("University Entrances" == syllabusSelect[syllabusSelect.selectedIndex].value) {
                            gradeSelect.options[0].innerHTML = "Select University";
                        } else
                            gradeSelect.options[0].innerHTML = "Select Exam";
                    } else if ("College" == levelSelect[levelSelect.selectedIndex].value) {
                        syllabusSelect.options[0].innerHTML = "Select Course";
                        gradeSelect.options[0].innerHTML = "Select Semester";
                    } else if ("General" == levelSelect[levelSelect.selectedIndex].value) {
                        syllabusSelect.options[0].innerHTML = "Select Type";
                        gradeSelect.options[0].innerHTML = "Select Genre";
                    } else if ("Magazine" == levelSelect[levelSelect.selectedIndex].value) {
                        syllabusSelect.options[0].innerHTML = "Select Type";
                        if ("Current Affairs" == syllabusSelect[syllabusSelect.selectedIndex].value) {
                            gradeSelect.options[0].innerHTML = "Select Exam";
                        } else if ("Know Your State" == syllabusSelect[syllabusSelect.selectedIndex].value) {
                            gradeSelect.options[0].innerHTML = "Select State";
                        }
                    }
                }
                <%}%>

                isAjaxCalled = false;
                displayBooks(books);

            }
            else{
                isAjaxCalled = true;
                allBooksLoaded = true;
                document.getElementById("load-more-button").style.display = 'none';

                if (!prepjoySite) {
                    document.querySelector('.loading-icon').classList.add('hidden');
                } else {
                    document.getElementById('loading').style.display = 'none';
                }
                if (subscriptionBooks) {
                    document.getElementById("content-data-books-ebooks").innerHTML = "<div class='ai-no-results'><div class='ai-no-results-title'>No books found.</div></div>";
                    subscriptionBooks = false;
                }
                document.getElementById("content-data-books-ebooks").innerHTML = "<div class='ai-no-results'>" +
                    "<div class='ai-no-results-icon'><i class='material-icons'>sentiment_dissatisfied</i></div>" +
                    "<div class='ai-no-results-title'>No Books Found</div>" +
                    "<div class='ai-no-results-text'>Please clear all filters and try again. You can also use the search feature to find specific books.</div>" +
                    "<button onclick='resetFilters()' class='ai-clear-filters mt-3'>Clear All Filters</button>" +
                    "</div>";
            }
        }
    }

    // Enhanced color bank for modern look
    var colorBank = ["#667eea", "#764ba2", "#f093fb", "#f5576c", "#4facfe", "#00f2fe", "#43e97b", "#38f9d7", "#ffecd2", "#fcb69f", "#a8edea", "#fed6e3"];

    function truncate(text, maxLength) {
        if(text==null) return "";
        return text.length > maxLength ? text.slice(0, maxLength - 3) + '...' : text;
    }

    function createCover(grade, title, subject, level, syllabus, brand) {
        if(grade==null) grade="";
        if(title==null) title="";
        if(subject==null) subject="";
        if(level==null) level="";
        if(syllabus==null) syllabus="";
        var color = colorBank[Math.floor(Math.random() * colorBank.length)];

        var gradeText = grade;
        if(level === "School"){
            gradeText = !grade.includes("Class") ? "Class " + grade : grade;
        }else if(level === "College"){
            gradeText = !grade.includes("Semester") ? "Semester " + grade : grade;
        }

        var subjectText = subject;
        if(subjectText === "All" || subjectText === "All subjects"){
            subjectText = syllabus;
        }

        var coverHtmlStr = "<div class=\"dummy_cover\">";
        if(brand){
            coverHtmlStr += "<div class=\"dummy_cover-section top\" style=\"display: flex !important;align-items: center\">";
        }else {
            coverHtmlStr += "<div class=\"dummy_cover-section top\">";
        }
        coverHtmlStr += "<span>" + truncate(gradeText, 10) + "</span>";
        if(brand){
            coverHtmlStr +="<span class=\"dummy_cover_brand\">iBook<span class=\"gpt\">GPT</span></span>";
        }
        coverHtmlStr += "</div>" +
                "<div class=\"dummy_cover-section middle\" style=\"background-color: " + color + ";\">" +
                    "<div class=\"dummy_cover_titleText\">" +truncate(title, 40) +
                    "</div>" +
                "</div>" +
                "<div class=\"dummy_cover-section bottom\">" +
                   truncate(subjectText, 15) +
                "</div>" +
            "</div>";
        return coverHtmlStr;
    }

    function setBlankState(){
        if(showPublishers) {
            document.getElementById("publisher").selectedIndex = 0;
        }
        document.getElementById("level").selectedIndex=0;
        document.getElementById("syllabus").selectedIndex=0;
        document.getElementById("grade").selectedIndex=0;
        document.getElementById("subject").selectedIndex=0;
        document.getElementById("level").classList.remove("background-bg");
        document.getElementById("syllabus").classList.remove("background-bg");
        document.getElementById("grade").classList.remove("background-bg");
        document.getElementById("subject").classList.remove("background-bg");
        if(showPublishers) document.getElementById("publisher").classList.remove("background-bg");

        subscriptionBooks = false;

        var syllabusSelect = document.getElementById("syllabus");
        var gradeSelect = document.getElementById("grade");
        var subjectSelect =  document.getElementById("subject");
       <%if(!"true".equals(session["doNotChangeLabel"])){%>
        syllabusSelect.options[0].innerHTML = "Select Board";
        gradeSelect.options[0].innerHTML = "Select Grade";
        subjectSelect.options[0].innerHTML = "Select Subject";
        <%}%>
    }

    function resetFilters(){
        <%if(session['appInApp']==null){%>
        var searchInputs = document.querySelectorAll('#search-book-header, #search-book-store, #search-book-home');
        searchInputs.forEach(function(input) {
            input.value = "";
        });
        <%}%>
        document.getElementById("content-data-books-ebooks").innerHTML="";
        pageNo=-1;
        allBooksLoaded=false;
        setBlankState();
        getBooks();
        catalogueLevel=null; catalogueSyllabus=null; catalogueGrade=null; catalogueSubject=null; cataloguePublisher=null;

        document.getElementById("resetFilter").removeAttribute('disabled');
        document.getElementById("resetFilter").style.color = '#007bff';
        document.getElementById("load-more-button").style.display = 'none';
        <%if("books".equals(""+session["entryController"])){%>
        document.body.classList.remove('showing-search-form');
        var searchCloseIcons = document.querySelectorAll(".search-close-icon,header .navbar-search");
        searchCloseIcons.forEach(function(el) { el.classList.add("d-none"); });
        var searchOpenIcons = document.querySelectorAll(".search-open-icon");
        searchOpenIcons.forEach(function(el) { el.classList.remove("d-none"); });
        <%}%>
        document.getElementById("amazonBooksTitle").innerHTML = "";
        document.getElementById("amazonBooksTitle").style.display = "none";
    }

    function levelChanged(field){
        allBooksLoaded=false;
        if(document.getElementById("level").selectedIndex>0) document.getElementById("level").classList.add("background-bg");
        else document.getElementById("level").classList.remove("background-bg");
        document.getElementById("syllabus").style.display = 'block';

        var searchShowHide = document.querySelectorAll(".searchShowHide");
        searchShowHide.forEach(function(el) { el.style.display = 'none'; });
        var searchEbooks = document.querySelectorAll(".search-ebooks");
        searchEbooks.forEach(function(el) { el.style.display = 'block'; });

        pageNo = -1;
        document.getElementById("content-data-books-ebooks").innerHTML="";
        getBooks();

        document.getElementById("resetFilter").removeAttribute('disabled');
        document.getElementById("resetFilter").style.color = '#007bff';
        document.getElementById("load-more-button").style.display = 'none';
    }

    function syllabusChanged(field) {
        allBooksLoaded=false;
        if(document.getElementById("syllabus").selectedIndex>0) document.getElementById("syllabus").classList.add("background-bg");
        else document.getElementById("syllabus").classList.remove("background-bg");

        var searchShowHide = document.querySelectorAll(".searchShowHide");
        searchShowHide.forEach(function(el) { el.style.display = 'none'; });
        var searchEbooks = document.querySelectorAll(".search-ebooks");
        searchEbooks.forEach(function(el) { el.style.display = 'block'; });

        document.getElementById("grade").style.display = 'block';
        document.getElementById("content-data-books-ebooks").innerHTML="";
        pageNo=-1;
        getBooks();
        document.getElementById("load-more-button").style.display = 'none';
    }

    function gradeChanged(field){
        allBooksLoaded=false;
        isAjaxCalled = true;

      if(document.getElementById("grade").selectedIndex>0) document.getElementById("grade").classList.add("background-bg");
        else document.getElementById("grade").classList.remove("background-bg");

        var searchShowHide = document.querySelectorAll(".searchShowHide");
        searchShowHide.forEach(function(el) { el.style.display = 'none'; });
        var searchEbooks = document.querySelectorAll(".search-ebooks");
        searchEbooks.forEach(function(el) { el.style.display = 'block'; });

        pageNo = -1;
        document.getElementById("content-data-books-ebooks").innerHTML = "";
        getBooks();
        document.getElementById("subject").style.display = 'block';
        document.getElementById("load-more-button").style.display = 'none';
    }

    function subjectChanged(field){
        allBooksLoaded=false;
        isAjaxCalled = true;
        pageNo = -1;
        document.getElementById("content-data-books-ebooks").innerHTML = "";
        if(document.getElementById("subject").selectedIndex>0) document.getElementById("subject").classList.add("background-bg");
        else document.getElementById("subject").classList.remove("background-bg");
        getBooks();

        var searchShowHide = document.querySelectorAll(".searchShowHide");
        searchShowHide.forEach(function(el) { el.style.display = 'none'; });
        var searchEbooks = document.querySelectorAll(".search-ebooks");
        searchEbooks.forEach(function(el) { el.style.display = 'block'; });
        document.getElementById("load-more-button").style.display = 'none';
    }

    function publisherChanged(field){
        allBooksLoaded=false;
        isAjaxCalled = true;
        document.getElementById("content-data-books-ebooks").innerHTML = "";
        pageNo = -1;

        getBooks();
        if(field.value) field.classList.add('background-bg');

        var publisherId = field[field.selectedIndex].value;
        var publisherName = field[field.selectedIndex].text;
        publisherName = publisherName.split(" ").join("-");

        var searchShowHide = document.querySelectorAll(".searchShowHide");
        searchShowHide.forEach(function(el) { el.style.display = 'none'; });
        var searchEbooks = document.querySelectorAll(".search-ebooks");
        searchEbooks.forEach(function(el) { el.style.display = 'block'; });

        document.getElementById("resetFilter").removeAttribute('disabled');
        document.getElementById("resetFilter").style.color = '#007bff';
        document.getElementById("load-more-button").style.display = 'none';
    }

    function pageInitialize(){
         var level=null,syllabus=null,grade=null,publisherId=null,subject=null,pageNo;
        <%if(params.level!=null){%>
          level =encodeURIComponent("${params.level.replace('~','&')}".replace('&amp;','&'));
        <%}else if(session.getAttribute('instituteLevel')!=null){%>
        level =encodeURIComponent("${session.getAttribute('instituteLevel').replace('~','&')}".replace('&amp;','&'));
        <%}%>
        <%if(params.syllabus!=null){%>
        syllabus = encodeURIComponent("${params.syllabus.replace('~','&')}".replace('&amp;','&'));
        <%}else if(params.level==null&&session.getAttribute('instituteSyllabus')!=null){%>
        syllabus =encodeURIComponent("${session.getAttribute('instituteSyllabus').replace('~','&')}".replace('&amp;','&'));
        <%}%>
        <%if(params.grade!=null){%>
        grade=encodeURIComponent("${params.grade.replace('~','&')}".replace('&amp;','&'));
        <%}%>
        <%if(params.subject!=null){%>
        subject=encodeURIComponent("${params.subject.replace('~','&')}".replace('&amp;','&'));
        <%}%>
        <%if(params.publisherId!=null){%>
        publisherId=encodeURIComponent("${params.publisherId}");
        <%}%>
        <%if(params.searchString!=null){%>
        var searchString = "${params.searchString}";
        document.getElementById("search-book-store").value = searchString;
        <%}%>

        getBooksWithParameters(level,syllabus,grade,subject,publisherId,0);
    }

    // Initialize page
    window.addEventListener("DOMContentLoaded", function() {
        pageInitialize();
    });

    // Amazon search results function (keeping original functionality)
    function amazonSearchResults(data){
        document.getElementById("resetFilter").removeAttribute('disabled');
        document.getElementById("resetFilter").style.color = '#007bff';
        if (!prepjoySite){
            document.querySelector('.loading-icon').classList.add('hidden');
        } else {
            document.getElementById('loading').style.display = 'none';
        }

        var books = JSON.parse(JSON.stringify(data.results));
        var ebookHtmlStr = "";

        for(var i=0;i<books.length;i++){
            var imgSrc = books[i].ImageUrl;
            var listPrice = books[i].ListPrice;
            var offerPrice = books[i].Price;
            var starTotal = 5;
            var starPercentage = (books[i].Rating / starTotal) * 100;
            var rating = Math.round(starPercentage / 5) * 5;

            ebookHtmlStr += "<div class='ai-book-card'>";
            ebookHtmlStr += "<div class='ai-book-image-container'>";
            ebookHtmlStr += "<a href='" +books[i].DetailPageURL+ "?tag=wonderslate-21&linkCode=w13' target='_blank'>";
            ebookHtmlStr += "<img src='" + imgSrc + "' alt='Book Cover Image' class='ai-book-image'/>";
            ebookHtmlStr += "</a></div>";
            ebookHtmlStr += "<div class='ai-book-content'>";
            ebookHtmlStr += "<a href='" +books[i].DetailPageURL+ "?tag=wonderslate-21&linkCode=w13' target='_blank' class='text-decoration-none'>";
            ebookHtmlStr += "<h3 class='ai-book-title'>"+ books[i].Title + "</h3></a>";
            ebookHtmlStr += "<p class='ai-book-publisher'>By " + books[i].Subtitle + "</p>";
            ebookHtmlStr += "<p class='ai-book-isbn'>ASIN: "+ books[i].ASIN +"</p>";

            if(books[i].Rating != null && books[i].Rating != "") {
                ebookHtmlStr += "<div class='mb-2'>";
                ebookHtmlStr += "<div class='stars-outer' style='display: inline-block; position: relative; font-family: FontAwesome; color: #ddd;'>";
                ebookHtmlStr += "<div class='stars-inner' style='position: absolute; top: 0; left: 0; white-space: nowrap; overflow: hidden; width: "+rating+"%; color: #f8ce0b;'>★★★★★</div>";
                ebookHtmlStr += "★★★★★</div>";
                ebookHtmlStr += "<span class='ms-2 text-muted'> "+ books[i].Rating + " (" + books[i].TotalReviews + " reviews)</span>";
                ebookHtmlStr += "</div>";
            }

            ebookHtmlStr += "<div class='ai-book-price-container'>";
            ebookHtmlStr += "<div class='ai-book-price'>";
            if(listPrice) {
                ebookHtmlStr += "<span class='ai-book-original-price'>" + listPrice + "</span>";
            }
            ebookHtmlStr += offerPrice;
            ebookHtmlStr += "</div></div>";
            ebookHtmlStr += "<div class='ai-book-actions'>";
            ebookHtmlStr += "<a href='" +books[i].DetailPageURL+ "?tag=wonderslate-21&linkCode=w13' target='_blank' class='ai-btn-primary'>";
            ebookHtmlStr += "<i class='material-icons' style='font-size: 14px;'>shopping_cart</i> Buy Now</a>";
            ebookHtmlStr += "</div>";
            ebookHtmlStr += "</div></div>";
        }

        if(books.length>0) {
            document.getElementById("content-data-books-ebooks").innerHTML = ebookHtmlStr;
            document.getElementById("content-data-books-ebooks").style.display = 'grid';
            document.getElementById("amazonBooksTitle").innerHTML = "<h3 class=\"text-center mb-0\">Books from Amazon and Flipkart</h3>";
            document.getElementById("amazonBooksTitle").style.display = "block";
            document.getElementById("noResultsFound").style.display = "none";
        } else {
            document.getElementById("noResultsFound").style.display = "block";
            document.getElementById("amazonBooksTitle").innerHTML = "";
            document.getElementById("amazonBooksTitle").style.display = "none";
        }
    }

</script>
            
            // Discount badge
            if (percentageVal > 0) {
                ebookHtmlStr += "<div class='ai-book-badge'>" + percentageVal + "% OFF</div>";
            }
            
            ebookHtmlStr += "</a></div>";
            
            // Book content
            ebookHtmlStr += "<div class='ai-book-content'>";
            
            // Title
            if(books[i].testTypeBook=="true") {
                ebookHtmlStr += "<a href='/wpmain/aiBookDtl?bookId=" + books[i].id +"' class='text-decoration-none'>";
            } else if(tokenId=="") {
                ebookHtmlStr += "<a href='/" + replaceAll(books[i].title,' ','-') + "/ebook-details?siteName="+siteName+"&bookId=" + books[i].id +"&publisher="+replaceAll(books[i].publisher,' ','-')+"&preview=true' target='_blank' class='text-decoration-none'>";
            }else{
                ebookHtmlStr += "<a href='/" + replaceAll(books[i].title,' ','-') +"/ebook-details?siteName="+siteName+"&bookId=" + books[i].id + "&preview=true&tokenId=" + tokenId + "' class='text-decoration-none'>";
            }
            ebookHtmlStr += "<h3 class='ai-book-title'>"+ books[i].title + "</h3></a>";
            
            // Publisher
            ebookHtmlStr += "<p class='ai-book-publisher'>By " + books[i].publisher + "</p>";
            
            // ISBN
            if (books[i].isbn != null && books[i].isbn != "") {
                ebookHtmlStr += "<p class='ai-book-isbn'>ISBN: "+books[i].isbn+"</p>";
            }
            
            // Price and actions (only for non-print books)
            if (!(books[i].bookType == "print"||subscriptionBooks||(wiley&&wileyCollectionBookId!=books[i].id))) {
                ebookHtmlStr += "<div class='ai-book-price-container'>";
                ebookHtmlStr += "<div class='ai-book-price'>";
                if(listPrice) {
                    ebookHtmlStr += "<span class='ai-book-original-price'>" + listPrice + "</span>";
                }
                ebookHtmlStr += offerPrice;
                if (books[i].validityDays == "365") {
                    ebookHtmlStr += "<small class='text-muted ms-1'>for 1 year</small>";
                }
                ebookHtmlStr += "</div></div>";
                
                // Action buttons
                ebookHtmlStr += "<div class='ai-book-actions'>";
                if(books[i].bookType === "bookgpt" || books[i].bookType ==="ebookwithai"){
                    if(books[i].testTypeBook=="true")
                        ebookHtmlStr +="<a href='/wpmain/aibook?siteName="+siteName+"&bookId="+books[i].id+"' class='ai-btn-primary'><i class='material-icons' style='font-size: 14px;'>smart_toy</i> Try AI</a>";
                    else
                        ebookHtmlStr +="<a href='/prompt/bookgpt?siteName="+siteName+"&bookId="+books[i].id+"' class='ai-btn-primary'><i class='material-icons' style='font-size: 14px;'>smart_toy</i> Try AI</a>";
                }else if(books[i].printOnly == false){
                    ebookHtmlStr +="<a href='/" + replaceAll(books[i].title,' ','-') + "/ebook?siteName="+siteName+"&bookId=" + books[i].id+"&preview=true' class='ai-btn-primary'><i class='material-icons' style='font-size: 14px;'>preview</i> Preview</a>";
                }
                ebookHtmlStr += "</div>";
            }
            
            ebookHtmlStr += "</div></div>";
            
            noOfEbooks++;
        }

        // Add all cards to the grid
        document.getElementById("content-data-books-ebooks").innerHTML += ebookHtmlStr;

        // Handle display logic
        if(noOfEbooks>0)   {
            if(searchMode){
                document.getElementById("load-more-button").style.display = 'none';
                searchMode = false;
                if(searchStatus=="Nothing present"){
                    if ("${session['siteId']}" == 27 || "${session['siteId']}" == 1 ) {
                        if(!amazonSearch){
                            document.getElementById("amazonBooksTitle").innerHTML = "";
                            document.getElementById("amazonBooksTitle").style.display = "none";
                            document.getElementById("noResultsFound").style.display = "none";
                        }
                        amazonSearch = false;
                    } else {
                        document.getElementById("noResultsFound").style.display = 'block';
                    }
                    document.getElementById("load-more-button").style.display = 'none';
                }
            }
            else{
                document.getElementById("amazonBooksTitle").innerHTML = "";
                document.getElementById("amazonBooksTitle").style.display = "none";
                document.getElementById("noResultsFound").style.display = "none";
                if(noOfEbooks>=20)   {
                    document.getElementById("load-more-button").style.display = "block";
                    document.getElementById("view-more").innerHTML = "<i class=\"material-icons me-2\" style=\"font-size: 18px;\">expand_more</i>Load More Books";
                } else {
                    subscriptionBooks=false;
                    document.getElementById("load-more-button").style.display = 'none';
                }
            }
        } else {
            document.getElementById("load-more-button").style.display = 'none';
            document.getElementById("content-data-books-ebooks").innerHTML = "<div class='ai-no-results'>" +
                "<div class='ai-no-results-icon'><i class='material-icons'>sentiment_dissatisfied</i></div>" +
                "<div class='ai-no-results-title'>No Books Found</div>" +
                "<div class='ai-no-results-text'>We couldn't find any books matching your criteria. Please try clearing filters or adjusting your search.</div>" +
                "<button onclick='resetFilters()' class='ai-clear-filters mt-3'>Clear All Filters</button>" +
                "</div>";
        }

        if (!prepjoySite){
            document.querySelector('.loading-icon').classList.add('hidden');
        }else {
            document.getElementById('loading').style.display = 'none';
        }

        // Apply colors to dummy covers
        var fronts = document.querySelectorAll(".uncover");
        for(var i=0 ; i < fronts.length; i++) {
            fronts[i].style.background = colors[i%12];
        }

        // Add loading effect for links
        var links = document.querySelectorAll("#content-data-books-ebooks a");
        links.forEach(function(link) {
            link.addEventListener("click", function() {
                if (!prepjoySite){
                    document.querySelector('.loading-icon').classList.remove('hidden');
                }else {
                    document.getElementById('loading').style.display = 'block';
                }
            });
        });
    }
