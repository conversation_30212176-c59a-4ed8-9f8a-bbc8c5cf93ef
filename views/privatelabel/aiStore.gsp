<%@ page import="javax.servlet.http.Cookie" %>
<%String requestURL = request.getRequestURL().toString();
String servletPath = request.getServletPath();
String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));

session.setAttribute("servername", appURL);
def newCookie = new javax.servlet.http.Cookie( "siteName", "privatelabel");
newCookie.path = "/"
response.addCookie newCookie;
%>
<g:render template="/privatelabel/navheader_new"></g:render>
<g:render template="/whitelabel/aiStoreDisplay"></g:render>

<g:render template="/privatelabel/footer_new"></g:render>
<g:render template="/resources/shareContent"></g:render>

<g:render template="/whitelabel/aiStoreLogic"></g:render>

<style>
/* Modern AI Store Styles */
.ai-store-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.ai-store-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.ai-store-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    margin-bottom: 1rem;
}

.ai-store-subtitle {
    text-align: center;
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 0;
}

.ai-filters-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.ai-filter-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ai-filter-icon {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.ai-books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
    padding: 1rem 0;
}

.ai-book-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.ai-book-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.ai-book-image-container {
    position: relative;
    height: 220px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.ai-book-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.ai-book-card:hover .ai-book-image {
    transform: scale(1.05);
}

.ai-book-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
}

.ai-book-content {
    padding: 1.5rem;
}

.ai-book-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.5rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.ai-book-publisher {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.ai-book-isbn {
    color: #adb5bd;
    font-size: 0.8rem;
    margin-bottom: 1rem;
}

.ai-book-price-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.ai-book-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #28a745;
}

.ai-book-original-price {
    color: #dc3545;
    text-decoration: line-through;
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

.ai-book-actions {
    display: flex;
    gap: 0.5rem;
}

.ai-btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.ai-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.ai-btn-secondary {
    background: rgba(108, 117, 125, 0.1);
    border: 1px solid rgba(108, 117, 125, 0.2);
    color: #6c757d;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.ai-btn-secondary:hover {
    background: rgba(108, 117, 125, 0.2);
    color: #495057;
    text-decoration: none;
}

.ai-load-more {
    text-align: center;
    margin: 3rem 0;
}

.ai-load-more-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.ai-load-more-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.ai-no-results {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    margin: 2rem 0;
}

.ai-no-results-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.ai-no-results-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.ai-no-results-text {
    color: #6c757d;
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ai-books-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .ai-store-title {
        font-size: 2rem;
    }
    
    .ai-book-content {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .ai-books-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .ai-store-container {
        padding: 1rem 0;
    }
    
    .ai-store-header,
    .ai-filters-container {
        margin: 0 1rem 1rem 1rem;
        padding: 1rem;
    }
}

/* Loading Animation */
.ai-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.ai-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.1);
    border-left: 4px solid #667eea;
    border-radius: 50%;
    animation: ai-spin 1s linear infinite;
}

@keyframes ai-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Form Controls */
.ai-form-control {
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.ai-form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.ai-clear-filters {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.ai-clear-filters:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}
</style>

<script>
// Enhanced hover effects for book cards
document.addEventListener('DOMContentLoaded', function() {
    const bookCards = document.querySelectorAll('.ai-book-card');
    
    bookCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
