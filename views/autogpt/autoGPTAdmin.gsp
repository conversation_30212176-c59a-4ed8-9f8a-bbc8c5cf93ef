<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}

/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

.form-group a {
    color: white;
}

/* Status filter styles */
.status-filters {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.status-filter-btn {
    margin-right: 10px;
    margin-bottom: 5px;
}

.status-filter-btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

/* Table styles */
.admin-table {
    font-size: 14px;
}

.admin-table th {
    background-color: #343a40;
    color: white;
    font-weight: bold;
    text-align: center;
    vertical-align: middle;
}

.admin-table td {
    vertical-align: middle;
    text-align: center;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-running {
    background-color: #007bff;
    color: white;
}

.status-completed {
    background-color: #28a745;
    color: white;
}

.status-not-started {
    background-color: #6c757d;
    color: white;
}

.delete-btn {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    padding: 2px 8px;
    font-size: 11px;
}

.delete-btn:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

/* Pagination styles */
.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination-info {
    font-size: 14px;
    color: #6c757d;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: none;
    z-index: 9999;
}

.loading-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Bulk actions styles */
#bulkActionsContainer {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

#bulkDeleteBtn {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

#bulkDeleteBtn:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

#clearSelectionBtn {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
    margin-left: 10px;
}

#clearSelectionBtn:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

/* Checkbox styles */
.row-checkbox, #selectAllCheckbox {
    transform: scale(1.2);
    margin: 0;
}

/* Make checkboxes more visible */
th input[type="checkbox"], td input[type="checkbox"] {
    cursor: pointer;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <div id="loadingMessage">Loading data...</div>
    </div>
</div>

<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-12 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">AutoGPT Admin Dashboard</h3>

                    <!-- Status Filters -->
                    <div class="status-filters">
                        <h5>Filter by Status:</h5>
                        <button type="button" class="btn btn-outline-primary status-filter-btn active" data-status="running">
                            Running
                        </button>
                        <button type="button" class="btn btn-outline-success status-filter-btn" data-status="completed">
                            Completed
                        </button>
                        <button type="button" class="btn btn-outline-secondary status-filter-btn" data-status="not_yet_started">
                            Not Yet Started
                        </button>
                        <button type="button" class="btn btn-outline-secondary status-filter-btn" data-status="pdf_processing">
                            PDF Processing
                        </button>
                    <button type="button" class="btn btn-outline-secondary status-filter-btn" data-status="pdf_processed">
                        PDF Processed
                    </button>
                        <button type="button" class="btn btn-outline-dark status-filter-btn" data-status="all">
                            All
                        </button>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="form-group" id="bulkActionsContainer" style="display: none; margin-bottom: 15px;">
                        <button type="button" class="btn btn-danger" id="bulkDeleteBtn">
                            <i class="fa fa-trash"></i> Delete Selected (<span id="selectedCount">0</span>)
                        </button>
                        <button type="button" class="btn btn-secondary" id="clearSelectionBtn">
                            Clear Selection
                        </button>
                    </div>

                    <!-- Data Table -->
                    <div class="form-group table-responsive" id="intrst-area">
                        <table class="table table-bordered table-striped admin-table" id="autogptTable">
                            <thead>
                                <tr>
                                    <th style="width: 5%;">
                                        <input type="checkbox" id="selectAllCheckbox" title="Select All">
                                    </th>
                                    <th style="width: 8%;">Chapter ID</th>
                                    <th style="width: 18%;">Chapter Name</th>
                                    <th style="width: 18%;">Book Title</th>
                                    <th style="width: 11%;">Date Added</th>
                                    <th style="width: 11%;">Date Started</th>
                                    <th style="width: 11%;">Date Completed</th>
                                    <th style="width: 8%;">Time Taken</th>
                                    <th style="width: 10%;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="autogptTableBody">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="pagination-container">
                        <div class="pagination-info" id="paginationInfo">
                            <!-- Pagination info will be displayed here -->
                        </div>
                        <nav aria-label="Page navigation">
                            <ul class="pagination" id="paginationControls">
                                <!-- Pagination controls will be generated here -->
                            </ul>
                        </nav>
                    </div>

                    <!-- No Data Message -->
                    <div id="noDataMessage" style="display: none; text-align: center; padding: 40px;">
                        <h5>No records found for the selected status.</h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
    // Global variables
    var currentStatus = "running";
    var currentPage = 0;
    var pageSize = 50;
    var totalPages = 0;
    var totalCount = 0;

    // Initialize the page
    $(document).ready(function() {
        setupEventListeners();
        loadData();
    });

    function setupEventListeners() {
        // Status filter buttons
        $(".status-filter-btn").click(function() {
            $(".status-filter-btn").removeClass("active");
            $(this).addClass("active");
            currentStatus = $(this).data("status");
            currentPage = 0;
            loadData();
        });

        // Select all checkbox
        $("#selectAllCheckbox").change(function() {
            var isChecked = $(this).is(":checked");
            $(".row-checkbox").prop("checked", isChecked);
            updateBulkActionsVisibility();
        });

        // Bulk delete button
        $("#bulkDeleteBtn").click(function() {
            bulkDeleteLogs();
        });

        // Clear selection button
        $("#clearSelectionBtn").click(function() {
            clearSelection();
        });
    }

    function showLoading(message) {
        $("#loadingMessage").text(message || "Loading data...");
        $("#loadingOverlay").show();
    }

    function hideLoading() {
        $("#loadingOverlay").hide();
    }

    function loadData() {
        showLoading("Loading AutoGPT data...");

        $.ajax({
            url: "/autogpt/getAutoGPTAdminData",
            type: "GET",
            data: {
                status: currentStatus,
                page: currentPage,
                pageSize: pageSize
            },
            success: function(response) {
                hideLoading();
                if (response.status === "OK") {
                    displayData(response);
                    updatePagination(response);
                } else {
                    showError("Error loading data: " + response.message);
                }
            },
            error: function(xhr, status, error) {
                hideLoading();
                showError("Error loading data: " + error);
            }
        });
    }

    function displayData(response) {
        var tableBody = $("#autogptTableBody");
        tableBody.empty();

        if (response.data && response.data.length > 0) {
            $("#autogptTable").show();
            $("#noDataMessage").hide();

            for (var i = 0; i < response.data.length; i++) {
                var row = response.data[i];
                var statusBadge = getStatusBadge(row.gptStatus);
                var actionButtons = getActionButtons(row);

                // Ensure we have a valid ID
                var rowId = row.id || "unknown_" + i;

                var rowHtml = "<tr>" +
                    "<td><input type='checkbox' class='row-checkbox' data-id='" + rowId + "' value='" + rowId + "' onchange='updateBulkActionsVisibility()'></td>" +
                    "<td>" + (row.chapterId || "-") + "</td>" +
                    "<td style='text-align: left;'>" + (row.chapterName || "-") + "</td>" +
                    "<td style='text-align: left;'><a href='/book-create-new?bookId=" + row.bookId + "' target='_blank' style='color: #007bff;'>" + (row.bookTitle || "-") + "</td>" +
                    "<td>" + (row.dateAdded || "-") + "</td>" +
                    "<td>" + (row.dateStarted || "-") + "</td>" +
                    "<td>" + (row.dateCompleted || "-") + "</td>" +
                    "<td>" + (row.timeTaken || "-") + "</td>" +
                    "<td>" + actionButtons + "</td>" +
                    "</tr>";

                tableBody.append(rowHtml);
            }
        } else {
            $("#autogptTable").hide();
            $("#noDataMessage").show();
        }

        // Reset bulk actions visibility
        updateBulkActionsVisibility();
    }

    function getStatusBadge(status) {
        if (status === "running") {
            return "<span class='status-badge status-running'>Running</span>";
        } else if (status === "completed") {
            return "<span class='status-badge status-completed'>Completed</span>";
        } else if (status === null || status === undefined) {
            return "<span class='status-badge status-not-started'>Not Started</span>";
        } else {
            return "<span class='status-badge'>" + status + "</span>";
        }
    }

    function getActionButtons(row) {
        var buttons = "";

        // Ensure we have a valid ID
        var rowId = row.id || "unknown";

        // Show delete button for all statuses
        buttons += "<button class='btn btn-sm delete-btn' onclick='deleteLog(" + rowId + ")' title='Delete'>" +
                   "<i class='fa fa-trash'></i> Delete" +
                   "</button>";

        return buttons;
    }

    function deleteLog(logId) {
        if (confirm("Are you sure you want to delete this AutoGPT log? This action cannot be undone.")) {
            showLoading("Deleting log...");

            $.ajax({
                url: "/autogpt/deleteAutoGPTLog",
                type: "POST",
                data: {
                    ids: [logId]
                },
                success: function(response) {
                    hideLoading();
                    if (response.status === "OK") {
                        alert("AutoGPT log deleted successfully!");
                        loadData(); // Reload the current page
                    } else {
                        showError("Error deleting log: " + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    showError("Error deleting log: " + error);
                }
            });
        }
    }

    function updateBulkActionsVisibility() {
        var selectedCheckboxes = $(".row-checkbox:checked");
        var selectedCount = selectedCheckboxes.length;

        $("#selectedCount").text(selectedCount);

        if (selectedCount > 0) {
            $("#bulkActionsContainer").show();
        } else {
            $("#bulkActionsContainer").hide();
        }

        // Update select all checkbox state
        var totalCheckboxes = $(".row-checkbox").length;

        if (selectedCount === 0) {
            $("#selectAllCheckbox").prop("indeterminate", false).prop("checked", false);
        } else if (selectedCount === totalCheckboxes) {
            $("#selectAllCheckbox").prop("indeterminate", false).prop("checked", true);
        } else {
            $("#selectAllCheckbox").prop("indeterminate", true);
        }
    }

    function bulkDeleteLogs() {
        var selectedCheckboxes = $(".row-checkbox:checked");
        var selectedIds = [];

        selectedCheckboxes.each(function() {
            // Try multiple ways to get the ID
            var id = $(this).attr("data-id") || $(this).data("id") || $(this).val();

            // Make sure we have a valid ID and it's not just the default checkbox value
            if (id && id !== "on" && id !== "" && id !== "undefined" && !id.toString().startsWith("unknown")) {
                selectedIds.push(id);
            }
        });

        if (selectedIds.length === 0) {
            alert("Please select at least one item to delete.");
            return;
        }

        var confirmMessage = "Are you sure you want to delete " + selectedIds.length + " AutoGPT log(s)? This action cannot be undone.";
        if (confirm(confirmMessage)) {
            showLoading("Deleting selected logs...");

            $.ajax({
                url: "/autogpt/deleteAutoGPTLog",
                type: "POST",
                data: {
                    ids: selectedIds
                },
                success: function(response) {
                    hideLoading();
                    if (response.status === "OK") {
                        alert("Selected AutoGPT logs deleted successfully!");
                        clearSelection();
                        loadData(); // Reload the current page
                    } else {
                        showError("Error deleting logs: " + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    showError("Error deleting logs: " + error);
                }
            });
        }
    }

    function clearSelection() {
        $(".row-checkbox").prop("checked", false);
        $("#selectAllCheckbox").prop("checked", false).prop("indeterminate", false);
        updateBulkActionsVisibility();
    }

    function updatePagination(response) {
        totalCount = response.totalCount;
        totalPages = response.totalPages;
        currentPage = response.currentPage;

        // Update pagination info
        var startRecord = (currentPage * pageSize) + 1;
        var endRecord = Math.min((currentPage + 1) * pageSize, totalCount);
        var paginationInfo = "Showing " + startRecord + " to " + endRecord + " of " + totalCount + " entries";
        $("#paginationInfo").text(paginationInfo);

        // Update pagination controls
        var paginationControls = $("#paginationControls");
        paginationControls.empty();

        if (totalPages > 1) {
            // Previous button
            var prevDisabled = currentPage === 0 ? "disabled" : "";
            paginationControls.append(
                "<li class='page-item " + prevDisabled + "'>" +
                "<a class='page-link' href='#' onclick='changePage(" + (currentPage - 1) + ")'>Previous</a>" +
                "</li>"
            );

            // Page numbers
            var startPage = Math.max(0, currentPage - 2);
            var endPage = Math.min(totalPages - 1, currentPage + 2);

            if (startPage > 0) {
                paginationControls.append(
                    "<li class='page-item'>" +
                    "<a class='page-link' href='#' onclick='changePage(0)'>1</a>" +
                    "</li>"
                );
                if (startPage > 1) {
                    paginationControls.append("<li class='page-item disabled'><span class='page-link'>...</span></li>");
                }
            }

            for (var i = startPage; i <= endPage; i++) {
                var activeClass = i === currentPage ? "active" : "";
                paginationControls.append(
                    "<li class='page-item " + activeClass + "'>" +
                    "<a class='page-link' href='#' onclick='changePage(" + i + ")'>" + (i + 1) + "</a>" +
                    "</li>"
                );
            }

            if (endPage < totalPages - 1) {
                if (endPage < totalPages - 2) {
                    paginationControls.append("<li class='page-item disabled'><span class='page-link'>...</span></li>");
                }
                paginationControls.append(
                    "<li class='page-item'>" +
                    "<a class='page-link' href='#' onclick='changePage(" + (totalPages - 1) + ")'>" + totalPages + "</a>" +
                    "</li>"
                );
            }

            // Next button
            var nextDisabled = currentPage === totalPages - 1 ? "disabled" : "";
            paginationControls.append(
                "<li class='page-item " + nextDisabled + "'>" +
                "<a class='page-link' href='#' onclick='changePage(" + (currentPage + 1) + ")'>Next</a>" +
                "</li>"
            );
        }
    }

    function changePage(page) {
        if (page >= 0 && page < totalPages && page !== currentPage) {
            currentPage = page;
            loadData();
        }
    }

    function showError(message) {
        alert("Error: " + message);
    }
</script>

</body>
</html>
