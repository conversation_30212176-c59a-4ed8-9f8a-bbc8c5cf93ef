package com.wonderslate.data

import grails.transaction.Transactional
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.sqlutil.SafeSql

@Transactional
class WpmainService {
    def grailsApplication

    def serviceMethod() {

    }

    def getExerciseSolutions(Long chapterId) {
        String sql = "SELECT om.id, om.question, om.answer, om.answer_description answerDescription, om.gpt_explanation gptExplanation, om.q_type qType " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name = 'Exercise Solutions' " +
                "AND rd.res_type = 'QA' " +
                "ORDER BY om.id"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        return results
    }

    def getQuestionBankData(Long chapterId) {
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)

        // Get QnA questions
        String qnaQuery = "SELECT om.id, om.question, om.answer, om.answer_description answerDescription, om.gpt_explanation gptExplanation, om.q_type qType, " +
                "om.option1, om.option2, om.option3, om.option4, om.option5 " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name = 'QuestionBank QnA' " +
                "AND rd.res_type = 'QA' " +
                "ORDER BY om.q_type, om.id"

        // Get MCQ questions
        String mcqQuery = "SELECT om.id, om.question, om.answer, om.answer_description answerDescription, om.gpt_explanation gptExplanation, om.q_type qType, " +
                "om.option1, om.option2, om.option3, om.option4, om.option5,om.answer1,om.answer2,om.answer3,om.answer4,om.answer5 " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name = 'QuestionBank MCQs' " +
                "AND rd.res_type = 'Multiple Choice Questions' " +
                "ORDER BY om.id"

        def qnaResults = sql1.rows(qnaQuery)
        def mcqResults = sql1.rows(mcqQuery)

        // Group QnA results by qType
        def groupedQnA = qnaResults.groupBy { it.qType }

        return [
            qnaQuestions: groupedQnA,
            mcqQuestions: mcqResults
        ]
    }

    def getQuestionTypeCounts(Long chapterId) {
        String sql = "SELECT om.q_type qType, COUNT(*) as count " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name IN ('QuestionBank QnA', 'QuestionBank MCQs') " +
                "AND rd.res_type IN ('QA', 'Multiple Choice Questions') " +
                "GROUP BY om.q_type"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        def counts = [:]
        results.each { row ->
            counts[row.qType] = row.count
        }

        return counts
    }

    def getExplanation(Long questionId) {
        ObjectiveMst question = ObjectiveMst.findById(questionId)
        return question?.gptExplanation ?: question?.answerDescription
    }

    def getBookOverviewData(Long bookId, List chaptersList) {
        def overviewData = []

        chaptersList.each { chapter ->
            def chapterData = [:]
            chapterData.chapterId = chapter.chapterId ?: chapter.id
            chapterData.chapterName = chapter.chapterName ?: chapter.name

            // Get question type counts for this chapter
            def questionTypeCounts = getQuestionTypeCounts(chapterData.chapterId as Long)

            // Get exercise solutions count
            def exerciseSolutions = getExerciseSolutions(chapterData.chapterId as Long)
            def exerciseCount = exerciseSolutions?.size() ?: 0

            chapterData.questionTypeCounts = questionTypeCounts
            chapterData.exerciseCount = exerciseCount
            chapterData.totalQuestions = questionTypeCounts.values().sum() ?: 0

            overviewData.add(chapterData)
        }

        return overviewData
    }

    def getBookLevelSummary(Long bookId, List chaptersList) {
        def bookSummary = [:]
        def totalCounts = [:]
        def totalExercises = 0

        // Initialize counters for each question type in the specified order
        def questionTypeOrder = [
            "LongAnswer",
            "ShortAnswer",
            "VeryShortAnswer",
            "AssertionReason",
            "Problem",
            "Multiple Choice Questions",
            "FillBlank",
            "TrueFalse",
            "MatchFollowing",
            "ArrangeSequence"
        ]

        questionTypeOrder.each { qType ->
            totalCounts[qType] = 0
        }

        // Aggregate data from all chapters
        chaptersList.each { chapter ->
            def chapterId = chapter.chapterId ?: chapter.id

            // Get question type counts for this chapter
            def questionTypeCounts = getQuestionTypeCounts(chapterId as Long)
            questionTypeCounts.each { qType, count ->
                if (totalCounts.containsKey(qType)) {
                    totalCounts[qType] += count
                }
            }

            // Get exercise solutions count
            def exerciseSolutions = getExerciseSolutions(chapterId as Long)
            totalExercises += exerciseSolutions?.size() ?: 0
        }

        bookSummary.questionTypeCounts = totalCounts
        bookSummary.totalExercises = totalExercises
        bookSummary.totalQuestions = totalCounts.values().sum() ?: 0
        bookSummary.totalChapters = chaptersList.size()

        return bookSummary
    }

    def getRelatedBooks(Long bookId, Long publisherId) {
        try {
            // Get related books from same publisher, excluding current book
            String sql = 'SELECT id, title, cover_image coverImage, price, listprice, rating, authors ' +
                        'FROM books_mst ' +
                        'WHERE publisher_id = ' + publisherId + ' ' +
                        'AND id != ' + bookId + ' ' +
                        'AND status = "published" ' +
                        'ORDER BY rating DESC, date_published DESC ' +
                        'LIMIT 6'

            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            return results
        } catch (Exception e) {
            println("Error getting related books for book ${bookId}: ${e.message}")
            return [] // Return empty list if there's an error
        }
    }

    def getTestimonials() {
        // Return dummy testimonials data
        return [
            [
                name: "Priya Sharma",
                role: "Class 12 Student",
                rating: 5,
                comment: "This AI-powered book has completely transformed my learning experience. The interactive explanations and practice questions are incredibly helpful!",
                avatar: "PS"
            ],
            [
                name: "Rajesh Kumar",
                role: "Teacher",
                rating: 5,
                comment: "As an educator, I'm impressed by the comprehensive content and AI-driven features. My students love the instant explanations and doubt clearing.",
                avatar: "RK"
            ],
            [
                name: "Anita Patel",
                role: "Parent",
                rating: 4,
                comment: "My daughter's grades have improved significantly since using this book. The structured approach and practice tests are excellent.",
                avatar: "AP"
            ],
            [
                name: "Vikram Singh",
                role: "Class 10 Student",
                rating: 5,
                comment: "The AI tutor feature is amazing! It's like having a personal teacher available 24/7. Highly recommended for all students.",
                avatar: "VS"
            ]
        ]
    }

    def getBookPricingInfo(Long bookId) {
        try {
            // Get pricing information from BookPriceDtl
            String sql = 'SELECT book_type bookType, list_price listPrice, sell_price sellPrice, currency_cd currencyCd ' +
                        'FROM wsshop.book_price_dtl ' +
                        'WHERE book_id = ' + bookId + ' ' +
                        'ORDER BY FIELD(book_type, "printbook", "eBook", "testSeries", "combo", "upgrade", "bookGPT")'

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)

            def pricingMap = [:]
            results.each { result ->
                pricingMap[result.bookType] = [
                    listPrice: result.listPrice,
                    sellPrice: result.sellPrice,
                    currency: result.currencyCd ?: 'INR'
                ]
            }

            return pricingMap
        } catch (Exception e) {
            println("Error getting pricing info for book ${bookId}: ${e.message}")
            // Return default pricing structure
            return [
                eBook: [
                    listPrice: 299.0,
                    sellPrice: 199.0,
                    currency: 'INR'
                ]
            ]
        }
    }

    def getNameSpace(Long chapterId) {
        try{
            String sql = "SELECT res_link, vector_stored FROM resource_dtl WHERE chapter_id = " + chapterId + " AND vector_stored IS NOT NULL"
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            if(results.size()>0 && results[0]!=null && results[0].res_link!=null && results[0].res_link.endsWith(".pdf")) {
                return results[0].vector_stored
            }
        }catch (Exception e){
            println("Error getting namespace for chapter ${chapterId}: ${e.message}")
            return null
        }
    }

    def getResId(Long chapterId) {
        try{
            String sql = "SELECT id FROM resource_dtl WHERE chapter_id = " + chapterId + " AND res_type = 'Notes' AND res_link LIKE '%.pdf'"
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            if(results.size()>0 && results[0]!=null) {
                return results[0].id
            }
        }catch (Exception e){
            println("Error getting resId for chapter ${chapterId}: ${e.message}")
            return null
        }
    }

    def deleteQuestion(Long questionId) {
        try {
            // Find the question in ObjectiveMst table
            ObjectiveMst question = ObjectiveMst.findById(questionId)

            if (!question) {
                return [success: false, message: "Question not found with ID: ${questionId}"]
            }

            // Delete the question
            question.delete(flush: true)

            log.info("Successfully deleted question with ID: ${questionId}")
            return [success: true, message: "Question deleted successfully"]

        } catch (Exception e) {
            log.error("Error deleting question with ID ${questionId}: ${e.message}", e)
            return [success: false, message: "Failed to delete question: ${e.message}"]
        }
    }
}
