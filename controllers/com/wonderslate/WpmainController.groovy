package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.cache.SecondDataProviderService
import com.wonderslate.data.BooksDtl
import com.wonderslate.data.BooksMst
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.data.WpmainService
import com.wonderslate.shop.BookPriceDtl
import com.wonderslate.sqlutil.SafeSql
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import com.wonderslate.data.ObjectiveMst
import grails.transaction.Transactional
import com.wonderslate.data.ChaptersMst
import com.wonderslate.shop.BookPriceDtl
import groovy.json.JsonSlurper

class WpmainController {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    SecondDataProviderService secondDataProviderService
    WpmainService wpmainService
    UtilService utilService
    UserManagementService userManagementService
    def redisService

    def index() {
        session["siteId"] = new Integer(1);
        if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }
    }

    def unmarkedQA() {
        String sql = " select count(*) from objective_mst where question is not null and answer is not null and marks is null"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        [numberOfQA: results[0][0]]
    }

    def updateUnMarkedQA() {
        //find all from ObjectiveMst where question and answer is not null and marks is null order by id desc and limit 100
        List questions = ObjectiveMst.findAllByQuestionIsNotNullAndAnswerIsNotNullAndMarksIsNull([sort: "id", order: "desc", max: 500])
        int numberUpdated = 0, numberFailed = 0;
        questions.each { question ->
            //logic to update marks
            try {
                def sentences = question.answer.split("\\.")
                if (sentences.length == 1) question.marks = new Double(1)
                else if (sentences.length == 2 || sentences.length == 3) question.marks = new Double(2)
                else if (sentences.length == 4 || sentences.length == 5) question.marks = new Double(3)
                else if (sentences.length == 6 || sentences.length == 7) question.marks = new Double(4)
                else question.marks = new Double(5)
                ObjectiveMst.executeUpdate("update ObjectiveMst set marks=" + question.marks + " where id=" + question.id)
                numberUpdated++
            }
            catch (Exception e) {
                numberFailed++
                println("Exception while updating marks for question id " + question.id)
            }
        }
        def json = [status: "success", updatedTime: new Date(), numberUpdated: numberUpdated, numberFailed: numberFailed]
        render json as JSON
    }

    @Transactional
    def bookai() {
        String bookId = params.bookId
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        if (redisService.("chapterId_resId_" + bookId) == null) {
            secondDataProviderService.getAllChapterIdResId(new Long(params.bookId));
        }
        List chaptersList = new JsonSlurper().parseText(redisService.("chapterId_resId_" + bookId))
        String userAgent = request.getHeader("User-Agent");
        Boolean isMobile = userAgent =~ /Mobile|Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/;
        def previewMode = true, hasBookAccess = true
        def showAIWindow = false

        // Get user roles for the template
        def userRoles = []
        if (springSecurityService.isLoggedIn()) {
            def user = springSecurityService.currentUser
            if (user) {
                userRoles = user.authorities?.collect { it.authority }
            }
        }

        [chaptersList   : chaptersList, bookId: bookId, isMobile: isMobile, previewMode: previewMode, hasBookAccess: hasBookAccess,
         bookTitle      : booksMst.title, showAIWindow: showAIWindow, userRoles: userRoles, title: "Teacher AI Assistant for " + booksMst.title,
         gptloaderName  : null,
         gptloaderpath  : null,
         gptcustomloader: null]
    }

    @Transactional
    def aibook() {
        if(params.bookId==null) params.bookId="312814"
        String bookId = params.bookId
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        boolean hasBookAccess = false
        def previewMode = false
        def gptManager = false
        if (springSecurityService.currentUser != null) {
            if (springSecurityService.currentUser.authorities.any {
                it.authority == "ROLE_GPT_MANAGER"
            }) {
                gptManager = true
            }
        }
        Integer siteId = utilService.getSiteId(request, session)

        if (springSecurityService.currentUser == null  || userManagementService.isValidSession(springSecurityService.currentUser.username, session.getId())) {
            println("Entered the permission check thingy")
            hasBookAccess = userManagementService.hasAccessToBook(new Long(bookId), session, request, true, response)
            if (!hasBookAccess) {
                hasBookAccess = userManagementService.hasLibraryAccessToBook(new Long(bookId), true)
            }
        }
        if (!hasBookAccess&&"published".equals(booksMst.status)) {
            previewMode = true
        }
        else if(!hasBookAccess){
            SiteMst siteMst = dataProviderService.getSiteMst(siteId)
            if ("true".equals(siteMst.commonWhiteLabel)) {
               redirect(uri: "/sp/${siteMst.siteName}/store")
            } else {
                redirect(controller: siteMst.siteName, action: 'index')
            }
            return
        }

        println("preview mode=" + previewMode)
        println("has access ="+hasBookAccess)
        if (redisService.("chapters_" + bookId) == null) {
            dataProviderService.getChaptersList(new Long(bookId))
        }
        println("chaptersList=" + redisService.("chapters_" + bookId))
        List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + bookId))
        BookPriceDtl bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Integer(bookId), "bookGPT")
        [booksMst: booksMst, chaptersList: chaptersList,previewMode: previewMode,bookPriceDtl: bookPriceDtl,gptManager:gptManager]
    }

    @Transactional
    def getChapterContent() {
        println("getChapterContent called with params: ${params}")
        Long chapterId = params.chapterId as Long

        def exerciseSolutions = wpmainService.getExerciseSolutions(chapterId)
        def questionBankData = wpmainService.getQuestionBankData(chapterId)
        def questionTypeCounts = wpmainService.getQuestionTypeCounts(chapterId)
        def namespace = wpmainService.getNameSpace(chapterId)
        def resId = wpmainService.getResId(chapterId)

        def result = [
            exerciseSolutions: exerciseSolutions,
            questionBank: questionBankData,
            questionTypeCounts: questionTypeCounts,
            namespace: namespace,
            resId: resId
        ]

        render result as JSON
    }

    @Transactional
    def getExplanation() {
        Long questionId = params.questionId as Long
        String explanation = wpmainService.getExplanation(questionId)

        render([explanation: explanation] as JSON)
    }

    @Transactional
    def deleteQuestion() {
        try {
            Long objId = params.objId as Long

            if (!objId) {
                render([success: false, message: "Question ID is required", objId: objId] as JSON)
                return
            }

            def result = wpmainService.deleteQuestion(objId)

            render([success: result.success, message: result.message, objId: objId] as JSON)

        } catch (Exception e) {
            log.error("Error deleting question: ${e.message}", e)
            render([success: false, message: "An error occurred while deleting the question", objId: params.objId] as JSON)
        }
    }

    @Transactional
    def aiBookDtl(){
        String bookId = params.bookId
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        BooksDtl booksDtl = dataProviderService.getBooksDtl(new Long(bookId))

        // Get chapters list for book overview
        if (redisService.("chapters_" + bookId) == null) {
            dataProviderService.getChaptersList(new Long(bookId))
        }
        List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + bookId))

        // Get book overview data for all chapters
        def bookOverviewData = wpmainService.getBookOverviewData(new Long(bookId), chaptersList)

        // Get book-level summary
        def bookLevelSummary = wpmainService.getBookLevelSummary(new Long(bookId), chaptersList)

        // Get testimonials
        def testimonials = wpmainService.getTestimonials()

        // Get pricing information from BookPriceDtl
        BookPriceDtl bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Integer(bookId), "bookGPT")

        [booksMst: booksMst, booksDtl: booksDtl, chaptersList: chaptersList, bookOverviewData: bookOverviewData,
         bookLevelSummary: bookLevelSummary, testimonials: testimonials,
         bookPriceDtl: bookPriceDtl, title: booksMst.title + " - Book Details"]
    }

}
