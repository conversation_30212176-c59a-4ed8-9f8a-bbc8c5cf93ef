package com.wonderslate.shop

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.WpmainService
import com.wonderslate.data.BooksMst
import groovy.json.JsonSlurper
import grails.transaction.Transactional

class PdfExporterController {

    PdfExporterService pdfExporterService
    WpmainService wpmainService
    DataProviderService dataProviderService
    def redisService
    def export() {
        def file = pdfExporterService.renderSampleToHtmlFile(
                "Sample Chapter",
                "supload/sample_chapter.html",params.view,params.chapterId
        )

        render "HTML saved to: ${file.absolutePath}"
    }

    def sample(){

    }

    def generatePdf() {
        String chapterTitle = "Math PDF"
        String htmlPath = "/Users/<USER>/Documents/Wonderslate/technical/puppeteer/puppeteer-test/sample1.html"
        String pdfPath = "/Users/<USER>/Documents/Wonderslate/technical/puppeteer/puppeteer-test/chapter1.pdf"

        boolean success = pdfExporterService.generatePdf(htmlPath, pdfPath)

        if (success) {
            render file: new File(pdfPath), contentType: 'application/pdf'
        } else {
            render status: 500, text: "PDF generation failed"
        }
    }

    def chapterPdf() {
        // No data needed - GSP will fetch via AJAX
        // Default chapter ID for testing if not provided
        Long chapterIdLong = Long.parseLong(params.chapterId)
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapterIdLong)

        // Get all required data using existing WpmainService methods
        def exerciseSolutions = wpmainService.getExerciseSolutions(chapterIdLong)
        def questionBankData = wpmainService.getQuestionBankData(chapterIdLong)
        def questionTypeCounts = wpmainService.getQuestionTypeCounts(chapterIdLong)

      [
                chapterName: "${chaptersMst.name}",
                exerciseSolutions: exerciseSolutions ?: [],
                questionBank: questionBankData ?: [:],
                questionTypeCounts: questionTypeCounts ?: [:]
        ]
    }

    def exportChapterPdf() {

        Long chapterId = params.chapterId as Long
        if (!chapterId) {
            render status: 400, text: "Chapter ID is required"
            return
        }

        try {
            // Generate HTML file
            String htmlPath = "/tmp/chapter_${chapterId}_${System.currentTimeMillis()}.html"
            String pdfPath = "/tmp/chapter_${chapterId}_${System.currentTimeMillis()}.pdf"

            File htmlFile = pdfExporterService.renderChapterToHtmlFile(chapterId, htmlPath)

            // Generate PDF
            boolean success = pdfExporterService.generatePdf(htmlPath, pdfPath)

            if (success) {
                File pdfFile = new File(pdfPath)
                if (pdfFile.exists()) {
                    response.setContentType('application/pdf')
                    response.setHeader('Content-Disposition', "attachment; filename=\"chapter_${chapterId}.pdf\"")
                    response.outputStream << pdfFile.bytes
                    response.outputStream.flush()

                    // Clean up temporary files
                    htmlFile.delete()
                    pdfFile.delete()
                } else {
                    render status: 500, text: "PDF file not generated"
                }
            } else {
                render status: 500, text: "PDF generation failed"
            }
        } catch (Exception e) {
            log.error("Error generating chapter PDF: ${e.message}", e)
            render status: 500, text: "Error generating PDF: ${e.message}"
        }
    }

    @Transactional
    def bookTitle() {
        String bookId = params.bookId
        if (!bookId) {
            render status: 400, text: "Book ID is required"
            return
        }

        try {
            // Get book details using the same methods as aiBookDtl
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))

            // Get chapters list for book overview
            if (redisService.("chapters_" + bookId) == null) {
                dataProviderService.getChaptersList(new Long(bookId))
            }
            List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + bookId))

            // Get book overview data for all chapters
            def bookOverviewData = wpmainService.getBookOverviewData(new Long(bookId), chaptersList)

            // Get book-level summary
            def bookLevelSummary = wpmainService.getBookLevelSummary(new Long(bookId), chaptersList)

            [
                booksMst: booksMst,
                bookOverviewData: bookOverviewData,
                bookLevelSummary: bookLevelSummary,
                chaptersList: chaptersList
            ]
        } catch (Exception e) {

            render status: 500, text: "Error loading book data: ${e.message}"
        }
    }
}
